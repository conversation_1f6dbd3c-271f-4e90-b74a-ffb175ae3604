import { relations } from "drizzle-orm"
import {
  boolean,
  jsonb,
  pgEnum,
  pgTable,
  text,
  timestamp,
  unique,
  uuid,
} from "drizzle-orm/pg-core"

import { users } from "./schema"

// Notification frequency enum
export const notificationFrequencyEnum = pgEnum("notification_frequency", [
  "immediate",
  "daily_digest",
  "weekly_digest",
  "never",
])

// Notification channel enum
export const notificationChannelEnum = pgEnum("notification_channel", [
  "email",
  "in_app",
  "sms",
])

// Notification type enum
export const notificationTypeEnum = pgEnum("notification_type", [
  // Application notifications
  "application_status",
  "application_deadline",

  // Matching notifications
  "match_result",
  "match_updates",

  // Interview notifications
  "interview_scheduled",
  "interview_reminder",
  "interview_feedback",

  // Deliverable notifications
  "deliverable_reminder",
  "deliverable_feedback",
  "deliverable_approved",

  // System notifications
  "system_announcements",
  "security_alerts",
  "account_updates",

  // Communication notifications
  "new_message",
  "support_ticket_update",
])

// Notification preferences table
export const notificationPreferences = pgTable(
  "notification_preferences",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    userId: uuid("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),

    // Global preferences
    globalEmail: boolean("global_email").notNull().default(true),
    globalInApp: boolean("global_in_app").notNull().default(true),
    globalSms: boolean("global_sms").notNull().default(false),

    // Email preferences
    emailFrequency: notificationFrequencyEnum("email_frequency")
      .notNull()
      .default("immediate"),
    emailDigestTime: text("email_digest_time"), // Time of day for digest emails (e.g., "09:00")

    // Type-specific preferences stored as JSON
    // Format: { [notificationType]: { email: boolean, in_app: boolean, sms: boolean } }
    typePreferences: jsonb("type_preferences")
      .$type<
        Record<
          string,
          {
            email: boolean
            in_app: boolean
            sms: boolean
          }
        >
      >()
      .default({}),

    // Unsubscribe token for email links
    unsubscribeToken: text("unsubscribe_token").unique(),

    // Timestamps
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at").defaultNow().notNull(),
  },
  (table) => ({
    userIdUnique: unique("notification_preferences_user_id_unique").on(
      table.userId
    ),
  })
)

// Notification log table - tracks sent notifications
export const notificationLogs = pgTable("notification_logs", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: uuid("user_id")
    .notNull()
    .references(() => users.id, { onDelete: "cascade" }),

  // Notification details
  type: notificationTypeEnum("type").notNull(),
  channel: notificationChannelEnum("channel").notNull(),
  subject: text("subject"),
  content: text("content").notNull(),

  // Tracking
  sentAt: timestamp("sent_at").defaultNow().notNull(),
  readAt: timestamp("read_at"),
  clickedAt: timestamp("clicked_at"),

  // Email-specific tracking
  emailMessageId: text("email_message_id"), // From email service provider
  emailOpened: boolean("email_opened").default(false),
  emailBounced: boolean("email_bounced").default(false),
  bounceReason: text("bounce_reason"),

  // Metadata
  metadata: jsonb("metadata"), // Additional context-specific data

  // Expiry for cleanup (notifications older than 90 days)
  expiresAt: timestamp("expires_at").notNull(),
})

// Relations
export const notificationPreferencesRelations = relations(
  notificationPreferences,
  ({ one }) => ({
    user: one(users, {
      fields: [notificationPreferences.userId],
      references: [users.id],
    }),
  })
)

export const notificationLogsRelations = relations(
  notificationLogs,
  ({ one }) => ({
    user: one(users, {
      fields: [notificationLogs.userId],
      references: [users.id],
    }),
  })
)

// Type exports
export type NotificationPreferences =
  typeof notificationPreferences.$inferSelect
export type NewNotificationPreferences =
  typeof notificationPreferences.$inferInsert
export type NotificationLog = typeof notificationLogs.$inferSelect
export type NewNotificationLog = typeof notificationLogs.$inferInsert
export type NotificationFrequency =
  (typeof notificationFrequencyEnum.enumValues)[number]
export type NotificationChannel =
  (typeof notificationChannelEnum.enumValues)[number]
export type NotificationType = (typeof notificationTypeEnum.enumValues)[number]

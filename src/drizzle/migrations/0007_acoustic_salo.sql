ALTER TABLE "student_profiles" ADD COLUMN "match_status" text DEFAULT 'pending' NOT NULL;--> statement-breakpoint
ALTER TABLE "student_profiles" ADD COLUMN "matched_organization_id" uuid;--> statement-breakpoint
ALTER TABLE "student_profiles" ADD COLUMN "matched_faculty_id" uuid;--> statement-breakpoint
ALTER TABLE "student_profiles" ADD COLUMN "program" text;--> statement-breakpoint
ALTER TABLE "student_profiles" ADD COLUMN "student_id" text;--> statement-breakpoint
ALTER TABLE "student_profiles" ADD COLUMN "statement" text;--> statement-breakpoint
ALTER TABLE "student_profiles" ADD COLUMN "statement_score" integer DEFAULT 0;--> statement-breakpoint
ALTER TABLE "student_profiles" ADD COLUMN "public_interest_rank" integer DEFAULT 0;--> statement-breakpoint
ALTER TABLE "student_profiles" ADD COLUMN "social_justice_rank" integer DEFAULT 0;--> statement-breakpoint
ALTER TABLE "student_profiles" ADD CONSTRAINT "student_profiles_matched_organization_id_users_id_fk" FOREIGN KEY ("matched_organization_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "student_profiles" ADD CONSTRAINT "student_profiles_matched_faculty_id_users_id_fk" FOREIGN KEY ("matched_faculty_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
-- Training Completion Tracking System
-- Migration: 0008_training_completion
-- Created: 2025-06-17
-- Purpose: Implement training module management and user completion tracking

-- Create training_modules table
CREATE TABLE IF NOT EXISTS "training_modules" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"title" text NOT NULL,
	"description" text NOT NULL,
	"content" text,
	"content_type" text DEFAULT 'internal' NOT NULL,
	"external_url" text,
	"is_required" boolean DEFAULT false NOT NULL,
	"minimum_score" integer,
	"duration_minutes" integer,
	"valid_from_date" timestamp,
	"valid_until_date" timestamp,
	"target_roles" jsonb DEFAULT '["student"]'::jsonb NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"display_order" integer DEFAULT 0 NOT NULL,
	"created_by" uuid NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Create user_training_progress table
CREATE TABLE IF NOT EXISTS "user_training_progress" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"training_module_id" uuid NOT NULL,
	"status" text DEFAULT 'not_started' NOT NULL,
	"progress" integer DEFAULT 0 NOT NULL,
	"score" integer,
	"started_at" timestamp,
	"completed_at" timestamp,
	"last_accessed_at" timestamp,
	"time_spent_minutes" integer DEFAULT 0 NOT NULL,
	"attempts" integer DEFAULT 0 NOT NULL,
	"certificate_issued" boolean DEFAULT false NOT NULL,
	"notes" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);

-- Add foreign key constraints
DO $$ BEGIN
 ALTER TABLE "training_modules" ADD CONSTRAINT "training_modules_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "user_training_progress" ADD CONSTRAINT "user_training_progress_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
 ALTER TABLE "user_training_progress" ADD CONSTRAINT "user_training_progress_training_module_id_training_modules_id_fk" FOREIGN KEY ("training_module_id") REFERENCES "training_modules"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS "training_modules_title_idx" ON "training_modules" ("title");
CREATE INDEX IF NOT EXISTS "training_modules_is_required_idx" ON "training_modules" ("is_required");
CREATE INDEX IF NOT EXISTS "training_modules_target_roles_idx" ON "training_modules" USING GIN ("target_roles");

CREATE INDEX IF NOT EXISTS "user_training_progress_user_idx" ON "user_training_progress" ("user_id");
CREATE INDEX IF NOT EXISTS "user_training_progress_status_idx" ON "user_training_progress" ("status");
CREATE INDEX IF NOT EXISTS "user_training_progress_completed_at_idx" ON "user_training_progress" ("completed_at");

-- Create unique constraint for user-training combination
DO $$ BEGIN
 ALTER TABLE "user_training_progress" ADD CONSTRAINT "user_training_progress_user_id_training_module_id_unique" UNIQUE("user_id","training_module_id");
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;

-- Insert mandatory Anti-Oppression and Cultural Sensitivity training
-- This will be created by the first admin user, so we'll use a placeholder for now
INSERT INTO "training_modules" (
    "title",
    "description", 
    "content",
    "content_type",
    "is_required",
    "target_roles",
    "valid_until_date",
    "duration_minutes",
    "display_order",
    "created_by"
) VALUES (
    'Anti-Oppression and Cultural Sensitivity Training',
    'Mandatory training for all students participating in the SALL program. Must be completed by January 31, 2025.',
    'This training covers fundamental concepts of anti-oppression and cultural sensitivity that are essential for working in diverse legal environments.',
    'internal',
    true,
    '["student"]'::jsonb,
    '2025-01-31 23:59:59'::timestamp,
    60,
    1,
    (SELECT id FROM users WHERE role = 'admin' LIMIT 1)
) ON CONFLICT DO NOTHING;

-- Add trigger for updating updated_at timestamps
CREATE OR REPLACE FUNCTION update_training_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_training_modules_updated_at
    BEFORE UPDATE ON training_modules
    FOR EACH ROW
    EXECUTE FUNCTION update_training_updated_at();

CREATE TRIGGER update_user_training_progress_updated_at
    BEFORE UPDATE ON user_training_progress
    FOR EACH ROW
    EXECUTE FUNCTION update_training_updated_at();
-- Migration: Add conflict resolution fields for concurrent edits
-- Following AWS Amplify DataStore and established conflict resolution patterns

-- Add conflict resolution fields to deliverables table
ALTER TABLE deliverables 
ADD COLUMN last_modified_by uuid REFERENCES users(id),
ADD COLUMN version_number integer NOT NULL DEFAULT 1,
ADD COLUMN edit_session_id uuid,
ADD COLUMN edit_started_at timestamp;

-- Add conflict resolution fields to deliverable_drafts table  
ALTER TABLE deliverable_drafts
ADD COLUMN client_version integer NOT NULL DEFAULT 1,
ADD COLUMN server_version integer NOT NULL DEFAULT 1,
ADD COLUMN conflict_detected boolean DEFAULT false,
ADD COLUMN last_conflict_at timestamp;

-- Add indexes for conflict detection performance
CREATE INDEX idx_deliverables_version_user ON deliverables(id, version_number, last_modified_by);
CREATE INDEX idx_drafts_conflict_detection ON deliverable_drafts(deliverable_id, client_version, server_version);
CREATE INDEX idx_edit_sessions ON deliverables(edit_session_id, edit_started_at) WHERE edit_session_id IS NOT NULL;

-- Create conflict resolution log table
CREATE TABLE conflict_resolution_log (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  entity_type text NOT NULL, -- 'deliverable', 'project', 'profile', etc.
  entity_id uuid NOT NULL,
  conflict_type text NOT NULL, -- 'concurrent_edit', 'version_mismatch', 'edit_session_expired'
  user_id uuid NOT NULL REFERENCES users(id),
  conflicting_user_id uuid REFERENCES users(id),
  client_version integer,
  server_version integer,
  resolution_strategy text, -- 'user_merge', 'server_wins', 'client_wins', 'manual'
  resolution_data jsonb,
  resolved_at timestamp,
  created_at timestamp DEFAULT NOW() NOT NULL
);

CREATE INDEX idx_conflict_log_entity ON conflict_resolution_log(entity_type, entity_id);
CREATE INDEX idx_conflict_log_user ON conflict_resolution_log(user_id, created_at);

-- Add conflict detection trigger for deliverables
CREATE OR REPLACE FUNCTION detect_deliverable_conflicts()
RETURNS TRIGGER AS $$
BEGIN
  -- Check for concurrent edits (multiple users editing within 5 minutes)
  IF EXISTS (
    SELECT 1 FROM deliverables 
    WHERE id = NEW.id 
    AND edit_session_id IS NOT NULL 
    AND edit_session_id != COALESCE(NEW.edit_session_id, gen_random_uuid())
    AND edit_started_at > NOW() - INTERVAL '5 minutes'
    AND last_modified_by != NEW.last_modified_by
  ) THEN
    -- Log concurrent edit conflict
    INSERT INTO conflict_resolution_log (
      entity_type, entity_id, conflict_type, user_id, conflicting_user_id,
      server_version, resolution_strategy
    ) VALUES (
      'deliverable', NEW.id, 'concurrent_edit', NEW.last_modified_by,
      (SELECT last_modified_by FROM deliverables WHERE id = NEW.id),
      NEW.version_number, 'detection_only'
    );
  END IF;

  -- Increment version number on content changes
  IF OLD IS NULL OR 
     OLD.updated_at != NEW.updated_at OR 
     OLD.current_revision_id != NEW.current_revision_id THEN
    NEW.version_number = COALESCE(OLD.version_number, 0) + 1;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for conflict detection
DROP TRIGGER IF EXISTS deliverable_conflict_detection ON deliverables;
CREATE TRIGGER deliverable_conflict_detection
  BEFORE UPDATE ON deliverables
  FOR EACH ROW
  EXECUTE FUNCTION detect_deliverable_conflicts();

-- Add conflict detection function for drafts
CREATE OR REPLACE FUNCTION detect_draft_conflicts()
RETURNS TRIGGER AS $$
DECLARE
  current_server_version integer;
BEGIN
  -- Get current server version from deliverable
  SELECT version_number INTO current_server_version
  FROM deliverables 
  WHERE id = NEW.deliverable_id;

  -- Check for version mismatch (optimistic locking)
  IF NEW.client_version < current_server_version THEN
    NEW.conflict_detected = true;
    NEW.last_conflict_at = NOW();
    NEW.server_version = current_server_version;
    
    -- Log version conflict
    INSERT INTO conflict_resolution_log (
      entity_type, entity_id, conflict_type, user_id,
      client_version, server_version, resolution_strategy
    ) VALUES (
      'deliverable_draft', NEW.deliverable_id, 'version_mismatch', NEW.user_id,
      NEW.client_version, current_server_version, 'client_notification'
    );
  ELSE
    NEW.conflict_detected = false;
    NEW.server_version = current_server_version;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for draft conflict detection
DROP TRIGGER IF EXISTS draft_conflict_detection ON deliverable_drafts;
CREATE TRIGGER draft_conflict_detection
  BEFORE INSERT OR UPDATE ON deliverable_drafts
  FOR EACH ROW
  EXECUTE FUNCTION detect_draft_conflicts();

-- Create function to start edit session (prevents conflicts)
CREATE OR REPLACE FUNCTION start_edit_session(
  p_deliverable_id uuid,
  p_user_id uuid
) RETURNS TABLE (
  session_id uuid,
  current_version integer,
  can_edit boolean,
  conflict_user_name text
) AS $$
DECLARE
  v_session_id uuid := gen_random_uuid();
  v_current_version integer;
  v_existing_session uuid;
  v_existing_user uuid;
  v_conflict_user_name text;
  v_can_edit boolean := true;
BEGIN
  -- Get current state
  SELECT version_number, edit_session_id, last_modified_by
  INTO v_current_version, v_existing_session, v_existing_user
  FROM deliverables 
  WHERE id = p_deliverable_id;

  -- Check for active edit session by another user
  IF v_existing_session IS NOT NULL 
     AND v_existing_user != p_user_id 
     AND (SELECT edit_started_at FROM deliverables WHERE id = p_deliverable_id) > NOW() - INTERVAL '10 minutes' 
  THEN
    -- Get conflicting user name
    SELECT CONCAT(name, ' (', email, ')') INTO v_conflict_user_name
    FROM users WHERE id = v_existing_user;
    
    v_can_edit := false;
    v_session_id := v_existing_session;
  ELSE
    -- Start new edit session
    UPDATE deliverables 
    SET edit_session_id = v_session_id,
        edit_started_at = NOW(),
        last_modified_by = p_user_id
    WHERE id = p_deliverable_id;
  END IF;

  RETURN QUERY SELECT v_session_id, v_current_version, v_can_edit, v_conflict_user_name;
END;
$$ LANGUAGE plpgsql;

-- Create function to end edit session
CREATE OR REPLACE FUNCTION end_edit_session(
  p_deliverable_id uuid,
  p_session_id uuid
) RETURNS boolean AS $$
BEGIN
  UPDATE deliverables 
  SET edit_session_id = NULL,
      edit_started_at = NULL
  WHERE id = p_deliverable_id 
    AND edit_session_id = p_session_id;
    
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- Create function to clean up stale edit sessions (run via cron)
CREATE OR REPLACE FUNCTION cleanup_stale_edit_sessions()
RETURNS integer AS $$
DECLARE
  cleaned_count integer;
BEGIN
  UPDATE deliverables 
  SET edit_session_id = NULL,
      edit_started_at = NULL
  WHERE edit_started_at < NOW() - INTERVAL '15 minutes';
  
  GET DIAGNOSTICS cleaned_count = ROW_COUNT;
  RETURN cleaned_count;
END;
$$ LANGUAGE plpgsql;
{"id": "a890d5cb-82bf-4e3a-af28-54807b888508", "prevId": "114ad494-9935-496b-a707-617f293f9469", "version": "7", "dialect": "postgresql", "tables": {"public.accounts": {"name": "accounts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true}, "provider_account_id": {"name": "provider_account_id", "type": "text", "primaryKey": false, "notNull": true}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false}, "token_type": {"name": "token_type", "type": "text", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "session_state": {"name": "session_state", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"accounts_user_id_users_id_fk": {"name": "accounts_user_id_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"accounts_provider_provider_account_id_unique": {"name": "accounts_provider_provider_account_id_unique", "nullsNotDistinct": false, "columns": ["provider", "provider_account_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.deliverables": {"name": "deliverables", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": false}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "due_date": {"name": "due_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "submitted_at": {"name": "submitted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "file_id": {"name": "file_id", "type": "uuid", "primaryKey": false, "notNull": false}, "feedback": {"name": "feedback", "type": "text", "primaryKey": false, "notNull": false}, "grade": {"name": "grade", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"deliverables_project_id_projects_id_fk": {"name": "deliverables_project_id_projects_id_fk", "tableFrom": "deliverables", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "deliverables_student_id_users_id_fk": {"name": "deliverables_student_id_users_id_fk", "tableFrom": "deliverables", "tableTo": "users", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "deliverables_file_id_files_id_fk": {"name": "deliverables_file_id_files_id_fk", "tableFrom": "deliverables", "tableTo": "files", "columnsFrom": ["file_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.faculty_profiles": {"name": "faculty_profiles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "department": {"name": "department", "type": "text", "primaryKey": false, "notNull": false}, "position": {"name": "position", "type": "text", "primaryKey": false, "notNull": false}, "research_areas": {"name": "research_areas", "type": "jsonb", "primaryKey": false, "notNull": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "office_location": {"name": "office_location", "type": "text", "primaryKey": false, "notNull": false}, "office_hours": {"name": "office_hours", "type": "text", "primaryKey": false, "notNull": false}, "can_supervise_students": {"name": "can_supervise_students", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "max_students": {"name": "max_students", "type": "integer", "primaryKey": false, "notNull": false, "default": 5}, "current_students_count": {"name": "current_students_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"faculty_profiles_user_id_users_id_fk": {"name": "faculty_profiles_user_id_users_id_fk", "tableFrom": "faculty_profiles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"faculty_profiles_user_id_unique": {"name": "faculty_profiles_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.files": {"name": "files", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "filename": {"name": "filename", "type": "text", "primaryKey": false, "notNull": true}, "original_name": {"name": "original_name", "type": "text", "primaryKey": false, "notNull": true}, "mime_type": {"name": "mime_type", "type": "text", "primaryKey": false, "notNull": true}, "size": {"name": "size", "type": "integer", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "s3_key": {"name": "s3_key", "type": "text", "primaryKey": false, "notNull": false}, "uploaded_by": {"name": "uploaded_by", "type": "uuid", "primaryKey": false, "notNull": true}, "purpose": {"name": "purpose", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"files_uploaded_by_users_id_fk": {"name": "files_uploaded_by_users_id_fk", "tableFrom": "files", "tableTo": "users", "columnsFrom": ["uploaded_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.organization_profiles": {"name": "organization_profiles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "organization_type": {"name": "organization_type", "type": "text", "primaryKey": false, "notNull": false}, "industry": {"name": "industry", "type": "text", "primaryKey": false, "notNull": false}, "size": {"name": "size", "type": "text", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "looking_for": {"name": "looking_for", "type": "jsonb", "primaryKey": false, "notNull": false}, "project_types": {"name": "project_types", "type": "jsonb", "primaryKey": false, "notNull": false}, "can_sponsor_internships": {"name": "can_sponsor_internships", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "paid_opportunities": {"name": "paid_opportunities", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "remote_available": {"name": "remote_available", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"organization_profiles_user_id_users_id_fk": {"name": "organization_profiles_user_id_users_id_fk", "tableFrom": "organization_profiles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"organization_profiles_user_id_unique": {"name": "organization_profiles_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.project_students": {"name": "project_students", "schema": "", "columns": {"project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "assigned_at": {"name": "assigned_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"project_students_project_id_projects_id_fk": {"name": "project_students_project_id_projects_id_fk", "tableFrom": "project_students", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "project_students_student_id_users_id_fk": {"name": "project_students_student_id_users_id_fk", "tableFrom": "project_students", "tableTo": "users", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"project_students_project_id_student_id_pk": {"name": "project_students_project_id_student_id_pk", "columns": ["project_id", "student_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.projects": {"name": "projects", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "faculty_id": {"name": "faculty_id", "type": "uuid", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "requirements": {"name": "requirements", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"projects_organization_id_users_id_fk": {"name": "projects_organization_id_users_id_fk", "tableFrom": "projects", "tableTo": "users", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "projects_faculty_id_users_id_fk": {"name": "projects_faculty_id_users_id_fk", "tableFrom": "projects", "tableTo": "users", "columnsFrom": ["faculty_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "session_token": {"name": "session_token", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"sessions_user_id_users_id_fk": {"name": "sessions_user_id_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"sessions_session_token_unique": {"name": "sessions_session_token_unique", "nullsNotDistinct": false, "columns": ["session_token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.student_profiles": {"name": "student_profiles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "has_been_matched": {"name": "has_been_matched", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "matched_reference": {"name": "matched_reference", "type": "text", "primaryKey": false, "notNull": false}, "matched_type": {"name": "matched_type", "type": "text", "primaryKey": false, "notNull": false}, "match_status": {"name": "match_status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "matched_organization_id": {"name": "matched_organization_id", "type": "uuid", "primaryKey": false, "notNull": false}, "matched_faculty_id": {"name": "matched_faculty_id", "type": "uuid", "primaryKey": false, "notNull": false}, "resume_id": {"name": "resume_id", "type": "uuid", "primaryKey": false, "notNull": false}, "cover_letter_id": {"name": "cover_letter_id", "type": "uuid", "primaryKey": false, "notNull": false}, "academic_year": {"name": "academic_year", "type": "text", "primaryKey": false, "notNull": false}, "major": {"name": "major", "type": "text", "primaryKey": false, "notNull": false}, "gpa": {"name": "gpa", "type": "numeric(3, 2)", "primaryKey": false, "notNull": false}, "skills": {"name": "skills", "type": "jsonb", "primaryKey": false, "notNull": false}, "interests": {"name": "interests", "type": "text", "primaryKey": false, "notNull": false}, "availability": {"name": "availability", "type": "text", "primaryKey": false, "notNull": false}, "program": {"name": "program", "type": "text", "primaryKey": false, "notNull": false}, "student_id": {"name": "student_id", "type": "text", "primaryKey": false, "notNull": false}, "statement": {"name": "statement", "type": "text", "primaryKey": false, "notNull": false}, "statement_score": {"name": "statement_score", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "public_interest_rank": {"name": "public_interest_rank", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "social_justice_rank": {"name": "social_justice_rank", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"student_profiles_user_id_users_id_fk": {"name": "student_profiles_user_id_users_id_fk", "tableFrom": "student_profiles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_profiles_matched_organization_id_users_id_fk": {"name": "student_profiles_matched_organization_id_users_id_fk", "tableFrom": "student_profiles", "tableTo": "users", "columnsFrom": ["matched_organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_profiles_matched_faculty_id_users_id_fk": {"name": "student_profiles_matched_faculty_id_users_id_fk", "tableFrom": "student_profiles", "tableTo": "users", "columnsFrom": ["matched_faculty_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_profiles_resume_id_files_id_fk": {"name": "student_profiles_resume_id_files_id_fk", "tableFrom": "student_profiles", "tableTo": "files", "columnsFrom": ["resume_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_profiles_cover_letter_id_files_id_fk": {"name": "student_profiles_cover_letter_id_files_id_fk", "tableFrom": "student_profiles", "tableTo": "files", "columnsFrom": ["cover_letter_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"student_profiles_user_id_unique": {"name": "student_profiles_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.support_ticket_messages": {"name": "support_ticket_messages", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "ticket_id": {"name": "ticket_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "is_internal": {"name": "is_internal", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"support_ticket_messages_ticket_id_support_tickets_id_fk": {"name": "support_ticket_messages_ticket_id_support_tickets_id_fk", "tableFrom": "support_ticket_messages", "tableTo": "support_tickets", "columnsFrom": ["ticket_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "support_ticket_messages_user_id_users_id_fk": {"name": "support_ticket_messages_user_id_users_id_fk", "tableFrom": "support_ticket_messages", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.support_tickets": {"name": "support_tickets", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "subject": {"name": "subject", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "priority": {"name": "priority", "type": "text", "primaryKey": false, "notNull": true, "default": "'medium'"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'open'"}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "assigned_to": {"name": "assigned_to", "type": "uuid", "primaryKey": false, "notNull": false}, "resolved_at": {"name": "resolved_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"support_tickets_user_id_users_id_fk": {"name": "support_tickets_user_id_users_id_fk", "tableFrom": "support_tickets", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "support_tickets_assigned_to_users_id_fk": {"name": "support_tickets_assigned_to_users_id_fk", "tableFrom": "support_tickets", "tableTo": "users", "columnsFrom": ["assigned_to"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_preferences": {"name": "user_preferences", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "theme": {"name": "theme", "type": "text", "primaryKey": false, "notNull": true}, "mode": {"name": "mode", "type": "text", "primaryKey": false, "notNull": true}, "radius": {"name": "radius", "type": "text", "primaryKey": false, "notNull": true}, "layout": {"name": "layout", "type": "text", "primaryKey": false, "notNull": true}, "direction": {"name": "direction", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"user_preferences_user_id_users_id_fk": {"name": "user_preferences_user_id_users_id_fk", "tableFrom": "user_preferences", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_preferences_user_id_unique": {"name": "user_preferences_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "organization_name": {"name": "organization_name", "type": "text", "primaryKey": false, "notNull": false}, "organization_title": {"name": "organization_title", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "email_verify_token": {"name": "email_verify_token", "type": "text", "primaryKey": false, "notNull": false}, "email_verified": {"name": "email_verified", "type": "timestamp", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "password_reset_token": {"name": "password_reset_token", "type": "text", "primaryKey": false, "notNull": false}, "password_reset_expires": {"name": "password_reset_expires", "type": "timestamp", "primaryKey": false, "notNull": false}, "avatar": {"name": "avatar", "type": "text", "primaryKey": false, "notNull": false}, "profile_background": {"name": "profile_background", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'ONLINE'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"name_username_status_created_at_idx": {"name": "name_username_status_created_at_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "username", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "nullsNotDistinct": false, "columns": ["username"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification_tokens": {"name": "verification_tokens", "schema": "", "columns": {"identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"verification_tokens_identifier_token_pk": {"name": "verification_tokens_identifier_token_pk", "columns": ["identifier", "token"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}
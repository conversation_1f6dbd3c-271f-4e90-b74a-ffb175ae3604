import { db } from "@/drizzle/db"
import { emailTemplates } from "@/drizzle/schema"
import { and, eq } from "drizzle-orm"
import { Resend } from "resend"

import type { Result } from "@/lib/result"

import { AuditService } from "@/lib/audit-service"
import { err, fromPromise, ok } from "@/lib/result"

// Validate Resend configuration
const isResendConfigured = Boolean(process.env.RESEND_API_KEY)
const emailFrom =
  process.env.EMAIL_FROM || "SA1L Externships <<EMAIL>>"

if (!isResendConfigured) {
  console.error(
    "Resend configuration error: Missing RESEND_API_KEY environment variable"
  )
}

// Initialize Resend client
const resend = new Resend(process.env.RESEND_API_KEY)

// Type definitions for email responses
interface EmailResponse {
  id: string
  from: string
  to: string[]
  created_at: string
}

// Template rendering function
function renderTemplate(
  template: string,
  variables: Record<string, unknown>
): string {
  return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
    return String(variables[key] || match)
  })
}

// Fetch template from database
async function getEmailTemplate(templateName: string): Promise<
  Result<
    {
      subject: string
      htmlContent: string
      textContent: string
    },
    Error
  >
> {
  try {
    const template = await db
      .select()
      .from(emailTemplates)
      .where(
        and(
          eq(emailTemplates.name, templateName),
          eq(emailTemplates.isActive, true)
        )
      )
      .limit(1)

    if (!template.length) {
      return err(new Error(`Email template '${templateName}' not found`))
    }

    // Update last used timestamp
    await db
      .update(emailTemplates)
      .set({ lastUsedAt: new Date() })
      .where(eq(emailTemplates.id, template[0].id))

    return ok({
      subject: template[0].subject,
      htmlContent: template[0].htmlContent,
      textContent: template[0].textContent,
    })
  } catch (error) {
    return err(
      new Error(`Failed to fetch email template: ${(error as Error).message}`)
    )
  }
}

// Base email sending function with Result pattern
async function sendEmail(params: {
  to: string | string[]
  subject: string
  html: string
  text: string
  replyTo?: string
}): Promise<Result<EmailResponse, Error>> {
  // Validate configuration
  if (!isResendConfigured) {
    return err(
      new Error("Email service is not configured. Check RESEND_API_KEY.")
    )
  }

  // Validate inputs
  if (!params.to || (Array.isArray(params.to) && params.to.length === 0)) {
    return err(new Error("Recipient email address is required"))
  }
  if (!params.subject || params.subject.trim() === "") {
    return err(new Error("Email subject is required"))
  }
  if (!params.html || params.html.trim() === "") {
    return err(new Error("Email HTML content is required"))
  }

  // Send email using Resend
  const result = await fromPromise(
    resend.emails.send({
      from: emailFrom,
      to: params.to,
      subject: params.subject,
      html: params.html,
      text: params.text,
      reply_to: params.replyTo,
    }) as Promise<EmailResponse>
  )

  if (!result.success) {
    console.error("Failed to send email:", result.error.message)
    return err(new Error(`Email delivery failed: ${result.error.message}`))
  }

  // Log email sent event
  await AuditService.logEmailEvent(
    "sent",
    `Email sent to ${Array.isArray(params.to) ? params.to.join(", ") : params.to}`,
    {
      subject: params.subject,
      recipients: Array.isArray(params.to) ? params.to : [params.to],
      emailId: result.data.id,
    }
  )

  return ok(result.data)
}

// Send email using database template
export async function sendTemplatedEmail(params: {
  to: string | string[]
  templateName: string
  variables: Record<string, unknown>
  replyTo?: string
}): Promise<Result<EmailResponse, Error>> {
  // Fetch template from database
  const templateResult = await getEmailTemplate(params.templateName)
  if (!templateResult.success) {
    return err(templateResult.error)
  }

  const { subject, htmlContent, textContent } = templateResult.data

  // Render templates with variables
  const renderedSubject = renderTemplate(subject, params.variables)
  const renderedHtml = renderTemplate(htmlContent, params.variables)
  const renderedText = renderTemplate(textContent, params.variables)

  // Send email
  return sendEmail({
    to: params.to,
    subject: renderedSubject,
    html: renderedHtml,
    text: renderedText,
    replyTo: params.replyTo,
  })
}

// Email templates
export async function sendVerificationEmail(
  to: string,
  token: string
): Promise<Result<EmailResponse, Error>> {
  if (!to || to.trim() === "") {
    return err(new Error("Email address is required"))
  }
  if (!token || token.trim() === "") {
    return err(new Error("Verification token is required"))
  }

  const verificationUrl = `${process.env.NEXT_PUBLIC_SERVER_URL}/auth/verify?token=${token}`

  // Try to use database template first
  const templateResult = await sendTemplatedEmail({
    to,
    templateName: "email_verification",
    variables: {
      verificationUrl,
      token,
      expiryTime: "24 hours",
    },
  })

  // If template doesn't exist, use hardcoded template
  if (
    !templateResult.success &&
    templateResult.error.message.includes("not found")
  ) {
    return sendEmail({
      to,
      subject: "Verify your email address",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Welcome to SA1L Externships!</h2>
          <p>Please verify your email address by clicking the link below:</p>
          <p style="margin: 30px 0;">
            <a href="${verificationUrl}" 
               style="background-color: #4F46E5; color: white; padding: 12px 24px; 
                      text-decoration: none; border-radius: 6px; display: inline-block;">
              Verify Email
            </a>
          </p>
          <p>Or copy and paste this link into your browser:</p>
          <p style="color: #6B7280; word-break: break-all;">${verificationUrl}</p>
          <p style="color: #6B7280; margin-top: 30px;">
            This link will expire in 24 hours. If you didn't request this email, 
            you can safely ignore it.
          </p>
        </div>
      `,
      text: `Welcome to SA1L Externships! Please verify your email address by visiting: ${verificationUrl}`,
    })
  }

  return templateResult
}

export async function sendPasswordResetEmail(
  to: string,
  token: string
): Promise<Result<EmailResponse, Error>> {
  if (!to || to.trim() === "") {
    return err(new Error("Email address is required"))
  }
  if (!token || token.trim() === "") {
    return err(new Error("Reset token is required"))
  }

  const resetUrl = `${process.env.NEXT_PUBLIC_SERVER_URL}/auth/reset-password?token=${token}`

  return sendEmail({
    to,
    subject: "Reset your password",
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Password Reset Request</h2>
        <p>You requested to reset your password. Click the link below to continue:</p>
        <p style="margin: 30px 0;">
          <a href="${resetUrl}" 
             style="background-color: #4F46E5; color: white; padding: 12px 24px; 
                    text-decoration: none; border-radius: 6px; display: inline-block;">
            Reset Password
          </a>
        </p>
        <p>Or copy and paste this link into your browser:</p>
        <p style="color: #6B7280; word-break: break-all;">${resetUrl}</p>
        <p style="color: #6B7280; margin-top: 30px;">
          This link will expire in 1 hour. If you didn't request this email, 
          you can safely ignore it and your password will remain unchanged.
        </p>
      </div>
    `,
    text: `Password Reset Request. Reset your password by visiting: ${resetUrl}`,
  })
}

// Application confirmation email
export async function sendApplicationConfirmation(
  to: string,
  studentName: string
): Promise<Result<EmailResponse, Error>> {
  if (!to || to.trim() === "") {
    return err(new Error("Email address is required"))
  }
  if (!studentName || studentName.trim() === "") {
    return err(new Error("Student name is required"))
  }

  return sendEmail({
    to,
    subject: "Application Received - SA1L Externship Programme",
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Application Received</h2>
        <p>Dear ${studentName},</p>
        <p>Thank you for submitting your application to the SA1L Externship Programme.</p>
        <p>We have successfully received your application and it is now under review. Here's what happens next:</p>
        <ul style="line-height: 1.8;">
          <li>Your application will be reviewed by our admissions team</li>
          <li>Statement grading will be completed within 5-7 business days</li>
          <li>The matching process will begin once all applications are reviewed</li>
          <li>You will be notified of your match results via email</li>
        </ul>
        <p>If you have any questions, please don't hesitate to contact us.</p>
        <p style="margin-top: 30px;">
          Best regards,<br>
          SA1L Externship Team
        </p>
      </div>
    `,
    text: `Dear ${studentName}, Thank you for submitting your application to the SA1L Externship Programme. We have successfully received your application and it is now under review.`,
  })
}

// Match notification for students
export async function sendStudentMatchNotification(
  to: string,
  studentName: string,
  matchType: "organization" | "faculty",
  matchName: string,
  nextSteps: string
): Promise<Result<EmailResponse, Error>> {
  if (!to || to.trim() === "") {
    return err(new Error("Email address is required"))
  }
  if (!studentName || !matchName) {
    return err(new Error("Student name and match name are required"))
  }

  const matchTypeLabel =
    matchType === "organization" ? "Organisation" : "Faculty Supervisor"

  return sendEmail({
    to,
    subject: `Match Result - SA1L Externship Programme`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Congratulations! You've Been Matched</h2>
        <p>Dear ${studentName},</p>
        <p>We are pleased to inform you that you have been matched for your externship placement!</p>
        <div style="background-color: #F3F4F6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p style="margin: 0;"><strong>${matchTypeLabel}:</strong> ${matchName}</p>
        </div>
        <h3>Next Steps:</h3>
        <div style="white-space: pre-line;">${nextSteps}</div>
        <p style="margin-top: 30px;">
          Please log in to your dashboard to view complete details and accept your match.
        </p>
        <p style="margin: 20px 0;">
          <a href="${process.env.NEXT_PUBLIC_SERVER_URL}/student/dashboard" 
             style="background-color: #4F46E5; color: white; padding: 12px 24px; 
                    text-decoration: none; border-radius: 6px; display: inline-block;">
            View Dashboard
          </a>
        </p>
        <p>Best regards,<br>SA1L Externship Team</p>
      </div>
    `,
    text: `Dear ${studentName}, Congratulations! You have been matched with ${matchName} for your externship placement. Please log in to view details and accept your match.`,
  })
}

// Interview invitation email
export async function sendInterviewInvitation(
  to: string,
  studentName: string,
  organizationName: string,
  interviewDate: Date,
  interviewLocation: string,
  interviewType: "virtual" | "in-person",
  meetingLink?: string,
  icsAttachment?: Buffer
): Promise<Result<EmailResponse, Error>> {
  if (!to || !studentName || !organizationName) {
    return err(new Error("Required fields missing"))
  }

  const formattedDate = new Intl.DateTimeFormat("en-GB", {
    dateStyle: "full",
    timeStyle: "short",
  }).format(interviewDate)

  const locationInfo =
    interviewType === "virtual"
      ? `<p><strong>Meeting Link:</strong> <a href="${meetingLink}">${meetingLink}</a></p>`
      : `<p><strong>Location:</strong> ${interviewLocation}</p>`

  return sendEmail({
    to,
    subject: `Interview Invitation - ${organizationName}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Interview Invitation</h2>
        <p>Dear ${studentName},</p>
        <p>${organizationName} would like to invite you for an interview regarding your externship application.</p>
        <div style="background-color: #F3F4F6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p style="margin: 5px 0;"><strong>Date & Time:</strong> ${formattedDate}</p>
          <p style="margin: 5px 0;"><strong>Interview Type:</strong> ${interviewType === "virtual" ? "Virtual Meeting" : "In-Person"}</p>
          ${locationInfo}
        </div>
        <p>Please confirm your attendance by logging into your dashboard.</p>
        ${icsAttachment ? '<p style="color: #6B7280;">A calendar invite is attached to this email.</p>' : ""}
        <p style="margin-top: 30px;">
          <a href="${process.env.NEXT_PUBLIC_SERVER_URL}/student/interviews" 
             style="background-color: #4F46E5; color: white; padding: 12px 24px; 
                    text-decoration: none; border-radius: 6px; display: inline-block;">
            View Interview Details
          </a>
        </p>
        <p>Best wishes,<br>${organizationName}</p>
      </div>
    `,
    text: `Dear ${studentName}, ${organizationName} invites you for an interview on ${formattedDate}. Please log in to confirm attendance.`,
  })
}

// Deliverable reminder email
export async function sendDeliverableReminder(
  to: string,
  studentName: string,
  deliverableName: string,
  dueDate: Date,
  daysUntilDue: number
): Promise<Result<EmailResponse, Error>> {
  if (!to || !studentName || !deliverableName) {
    return err(new Error("Required fields missing"))
  }

  const formattedDate = new Intl.DateTimeFormat("en-GB", {
    dateStyle: "long",
  }).format(dueDate)

  const urgencyText =
    daysUntilDue <= 3
      ? "urgent reminder"
      : daysUntilDue <= 7
        ? "reminder"
        : "upcoming deadline"

  return sendEmail({
    to,
    subject: `Deliverable ${urgencyText}: ${deliverableName}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>Deliverable Reminder</h2>
        <p>Dear ${studentName},</p>
        <p>This is a ${urgencyText} that your deliverable is due soon:</p>
        <div style="background-color: #FEF3C7; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #F59E0B;">
          <p style="margin: 5px 0;"><strong>Deliverable:</strong> ${deliverableName}</p>
          <p style="margin: 5px 0;"><strong>Due Date:</strong> ${formattedDate}</p>
          <p style="margin: 5px 0;"><strong>Days Remaining:</strong> ${daysUntilDue} days</p>
        </div>
        <p>Please ensure you submit your deliverable before the deadline.</p>
        <p style="margin: 20px 0;">
          <a href="${process.env.NEXT_PUBLIC_SERVER_URL}/student/deliverables" 
             style="background-color: #4F46E5; color: white; padding: 12px 24px; 
                    text-decoration: none; border-radius: 6px; display: inline-block;">
            Submit Deliverable
          </a>
        </p>
        <p>Best regards,<br>SA1L Externship Team</p>
      </div>
    `,
    text: `Dear ${studentName}, Reminder: Your ${deliverableName} is due on ${formattedDate} (${daysUntilDue} days remaining). Please submit before the deadline.`,
  })
}

// Faculty notification for new match
export async function sendFacultyMatchNotification(
  to: string,
  facultyName: string,
  studentName: string,
  projectTitle: string
): Promise<Result<EmailResponse, Error>> {
  if (!to || !facultyName || !studentName || !projectTitle) {
    return err(new Error("Required fields missing"))
  }

  return sendEmail({
    to,
    subject: `New Student Match - ${projectTitle}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>New Student Matched to Your Project</h2>
        <p>Dear ${facultyName},</p>
        <p>A student has been matched to your research project through the SA1L programme.</p>
        <div style="background-color: #F3F4F6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p style="margin: 5px 0;"><strong>Student:</strong> ${studentName}</p>
          <p style="margin: 5px 0;"><strong>Project:</strong> ${projectTitle}</p>
        </div>
        <p>Please log in to your dashboard to:</p>
        <ul>
          <li>Review the student's profile and qualifications</li>
          <li>Accept or decline the match</li>
          <li>Contact the student if accepted</li>
        </ul>
        <p style="margin: 20px 0;">
          <a href="${process.env.NEXT_PUBLIC_SERVER_URL}/faculty/matched-students" 
             style="background-color: #4F46E5; color: white; padding: 12px 24px; 
                    text-decoration: none; border-radius: 6px; display: inline-block;">
            Review Match
          </a>
        </p>
        <p>Best regards,<br>SA1L Externship Team</p>
      </div>
    `,
    text: `Dear ${facultyName}, A student (${studentName}) has been matched to your project: ${projectTitle}. Please log in to review and respond.`,
  })
}

// Organization notification for new match
export async function sendOrganizationMatchNotification(
  to: string,
  contactName: string,
  students: Array<{ name: string; programme: string }>,
  organizationName: string
): Promise<Result<EmailResponse, Error>> {
  if (!to || !contactName || !organizationName || students.length === 0) {
    return err(new Error("Required fields missing"))
  }

  const studentList = students
    .map((s) => `<li>${s.name} - ${s.programme}</li>`)
    .join("\n")

  const studentCount = students.length
  const studentText = studentCount === 1 ? "student has" : "students have"

  return sendEmail({
    to,
    subject: `New Student Match${studentCount > 1 ? "es" : ""} - SA1L Programme`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>New Student Match${studentCount > 1 ? "es" : ""}</h2>
        <p>Dear ${contactName},</p>
        <p>${studentCount} ${studentText} been matched to ${organizationName} for externship placements.</p>
        <div style="background-color: #F3F4F6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p style="margin-bottom: 10px;"><strong>Matched Students:</strong></p>
          <ul style="margin: 0;">
            ${studentList}
          </ul>
        </div>
        <p>Next steps:</p>
        <ol>
          <li>Review student profiles in your dashboard</li>
          <li>Schedule interviews with prospective candidates</li>
          <li>Accept or decline matches within 7 days</li>
        </ol>
        <p style="margin: 20px 0;">
          <a href="${process.env.NEXT_PUBLIC_SERVER_URL}/organization/matched-students" 
             style="background-color: #4F46E5; color: white; padding: 12px 24px; 
                    text-decoration: none; border-radius: 6px; display: inline-block;">
            View Matches
          </a>
        </p>
        <p>Best regards,<br>SA1L Externship Team</p>
      </div>
    `,
    text: `Dear ${contactName}, ${studentCount} ${studentText} been matched to ${organizationName}. Please log in to review profiles and schedule interviews.`,
  })
}

// Support ticket auto-creation notification
export async function sendSupportTicketNotification(
  to: string,
  adminName: string,
  ticketType: string,
  ticketDetails: string,
  studentInfo?: { name: string; email: string }
): Promise<Result<EmailResponse, Error>> {
  if (!to || !adminName || !ticketType) {
    return err(new Error("Required fields missing"))
  }

  const studentSection = studentInfo
    ? `
    <div style="background-colour: #F3F4F6; padding: 15px; border-radius: 6px; margin: 15px 0;">
      <p style="margin: 5px 0;"><strong>Student:</strong> ${studentInfo.name}</p>
      <p style="margin: 5px 0;"><strong>Email:</strong> ${studentInfo.email}</p>
    </div>
  `
    : ""

  return sendEmail({
    to,
    subject: `[Support Ticket] ${ticketType}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2>New Support Ticket Created</h2>
        <p>Hello ${adminName},</p>
        <p>A new support ticket has been automatically created:</p>
        <div style="background-color: #FEE2E2; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #EF4444;">
          <p style="margin: 5px 0;"><strong>Type:</strong> ${ticketType}</p>
          <p style="margin: 10px 0 5px 0;"><strong>Details:</strong></p>
          <p style="margin: 5px 0; white-space: pre-line;">${ticketDetails}</p>
        </div>
        ${studentSection}
        <p>Please review this ticket in the admin dashboard.</p>
        <p style="margin: 20px 0;">
          <a href="${process.env.NEXT_PUBLIC_SERVER_URL}/admin/support-tickets" 
             style="background-color: #4F46E5; color: white; padding: 12px 24px; 
                    text-decoration: none; border-radius: 6px; display: inline-block;">
            View Ticket
          </a>
        </p>
        <p>Best regards,<br>SA1L System</p>
      </div>
    `,
    text: `New support ticket: ${ticketType}. Details: ${ticketDetails}. Please review in the admin dashboard.`,
  })
}

import { randomUUID } from "crypto"

import {
  DeleteO<PERSON><PERSON>ommand,
  Get<PERSON><PERSON><PERSON>ommand,
  Put<PERSON><PERSON><PERSON>ommand,
  S3Client,
} from "@aws-sdk/client-s3"
import { Upload } from "@aws-sdk/lib-storage"
import { getSignedUrl } from "@aws-sdk/s3-request-presigner"

import type { Result } from "@/lib/result"

import { err, from<PERSON>rom<PERSON>, ok } from "@/lib/result"

// S3 configuration from environment variables
const bucketName = process.env.S3_BUCKET_NAME || ""
const region = process.env.AWS_REGION || "us-east-1"

// Validate required environment variables
const isConfigValid =
  bucketName &&
  process.env.AWS_ACCESS_KEY_ID &&
  process.env.AWS_SECRET_ACCESS_KEY

if (!isConfigValid) {
  console.error(
    "S3 configuration error: Missing required environment variables"
  )
  console.error(
    "Required: S3_BUCKET_NAME, AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY"
  )
}

// Create S3 client with configuration
const s3Client = new S3Client({
  region,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  },
})

/**
 * Generate S3 key based on file type and current year
 */
function generateS3Key(fileName: string, fileType: string): string {
  const year = new Date().getFullYear()
  const uuid = randomUUID()
  const extension = fileName.split(".").pop() || ""

  // Organize by year and file type
  return `${year}/${fileType}/${uuid}.${extension}`
}

/**
 * Upload a file to S3
 */
export async function uploadToS3(
  file: Buffer | Uint8Array | string,
  fileName: string,
  fileType: string,
  contentType: string
): Promise<Result<{ key: string; url: string }, Error>> {
  // Validate configuration
  if (!isConfigValid) {
    return err(
      new Error("S3 is not properly configured. Check environment variables.")
    )
  }

  // Validate inputs
  if (!file) {
    return err(new Error("File content is required"))
  }
  if (!fileName || fileName.trim() === "") {
    return err(new Error("File name is required"))
  }
  if (!contentType || contentType.trim() === "") {
    return err(new Error("Content type is required"))
  }

  const key = generateS3Key(fileName, fileType)

  // Use Upload utility for efficient multipart uploads
  const upload = new Upload({
    client: s3Client,
    params: {
      Bucket: bucketName,
      Key: key,
      Body: file,
      ContentType: contentType,
      // Add security headers
      ServerSideEncryption: "AES256",
      Metadata: {
        uploadedAt: new Date().toISOString(),
        originalName: fileName,
      },
    },
  })

  // Monitor upload progress (optional)
  upload.on("httpUploadProgress", (progress) => {
    if (progress.loaded && progress.total) {
      const percentage = Math.round((progress.loaded / progress.total) * 100)
      console.log(`Upload progress: ${percentage}%`)
    }
  })

  const uploadResult = await fromPromise(upload.done())
  if (!uploadResult.success) {
    console.error("S3 upload failed:", uploadResult.error)
    return err(
      new Error(`Failed to upload file to S3: ${uploadResult.error.message}`)
    )
  }

  // Generate a signed URL for the uploaded file (7 days expiry)
  const urlResult = await fromPromise(
    getSignedUrl(
      s3Client,
      new GetObjectCommand({
        Bucket: bucketName,
        Key: key,
      }),
      { expiresIn: 7 * 24 * 60 * 60 }
    ) // 7 days
  )

  if (!urlResult.success) {
    console.error("Failed to generate signed URL:", urlResult.error)
    return err(
      new Error(`Failed to generate signed URL: ${urlResult.error.message}`)
    )
  }

  return ok({ key, url: urlResult.data })
}

/**
 * Get a signed URL for downloading a file from S3
 */
export async function getSignedDownloadUrl(
  key: string,
  expiresIn = 3600
): Promise<Result<string, Error>> {
  // Validate configuration
  if (!isConfigValid) {
    return err(
      new Error("S3 is not properly configured. Check environment variables.")
    )
  }

  // Validate inputs
  if (!key || key.trim() === "") {
    return err(new Error("S3 key is required"))
  }
  if (expiresIn <= 0 || expiresIn > 604800) {
    // Max 7 days
    return err(new Error("Expiry time must be between 1 second and 7 days"))
  }

  const result = await fromPromise(
    getSignedUrl(
      s3Client,
      new GetObjectCommand({
        Bucket: bucketName,
        Key: key,
      }),
      { expiresIn }
    )
  )

  if (!result.success) {
    console.error("Failed to generate download URL:", result.error)
    return err(
      new Error(`Failed to generate download URL: ${result.error.message}`)
    )
  }

  return ok(result.data)
}

/**
 * Delete a file from S3
 */
export async function deleteFromS3(key: string): Promise<Result<void, Error>> {
  // Validate configuration
  if (!isConfigValid) {
    return err(
      new Error("S3 is not properly configured. Check environment variables.")
    )
  }

  // Validate inputs
  if (!key || key.trim() === "") {
    return err(new Error("S3 key is required for deletion"))
  }

  const result = await fromPromise(
    s3Client.send(
      new DeleteObjectCommand({
        Bucket: bucketName,
        Key: key,
      })
    )
  )

  if (!result.success) {
    console.error("Failed to delete from S3:", result.error)
    return err(
      new Error(`Failed to delete file from S3: ${result.error.message}`)
    )
  }

  return ok(undefined)
}

/**
 * Get file metadata from S3
 */
export async function getFileMetadata(key: string): Promise<
  Result<
    {
      contentType?: string
      contentLength?: number
      lastModified?: Date
    },
    Error
  >
> {
  // Validate configuration
  if (!isConfigValid) {
    return err(
      new Error("S3 is not properly configured. Check environment variables.")
    )
  }

  // Validate inputs
  if (!key || key.trim() === "") {
    return err(new Error("S3 key is required"))
  }

  const command = new GetObjectCommand({
    Bucket: bucketName,
    Key: key,
  })

  const result = await fromPromise(s3Client.send(command))

  if (!result.success) {
    console.error("Failed to get file metadata:", result.error)
    return err(
      new Error(`Failed to get file metadata: ${result.error.message}`)
    )
  }

  const response = result.data

  return ok({
    contentType: response.ContentType,
    contentLength: response.ContentLength,
    lastModified: response.LastModified,
  })
}

/**
 * Generate a presigned URL for direct browser upload
 */
export async function getPresignedUploadUrl(
  fileName: string,
  fileType: string,
  contentType: string,
  maxSizeBytes = 10 * 1024 * 1024 // 10MB default
): Promise<Result<{ uploadUrl: string; key: string }, Error>> {
  // Validate configuration
  if (!isConfigValid) {
    return err(
      new Error("S3 is not properly configured. Check environment variables.")
    )
  }

  // Validate inputs
  if (!fileName || fileName.trim() === "") {
    return err(new Error("File name is required"))
  }
  if (!contentType || contentType.trim() === "") {
    return err(new Error("Content type is required"))
  }
  if (maxSizeBytes <= 0) {
    return err(new Error("Max size must be positive"))
  }

  const key = generateS3Key(fileName, fileType)

  const command = new PutObjectCommand({
    Bucket: bucketName,
    Key: key,
    ContentType: contentType,
    ServerSideEncryption: "AES256",
    Metadata: {
      uploadedAt: new Date().toISOString(),
      originalName: fileName,
    },
  })

  const result = await fromPromise(
    getSignedUrl(s3Client, command, {
      expiresIn: 3600, // 1 hour
      signableHeaders: new Set(["content-type"]),
    })
  )

  if (!result.success) {
    console.error("Failed to generate presigned upload URL:", result.error)
    return err(
      new Error(
        `Failed to generate presigned upload URL: ${result.error.message}`
      )
    )
  }

  return ok({ uploadUrl: result.data, key })
}

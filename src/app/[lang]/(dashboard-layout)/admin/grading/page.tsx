import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"

import { authOptions } from "@/configs/next-auth"

import { GradingInterface } from "./_components/grading-interface"

export default async function AdminGradingPage() {
  const session = await getServerSession(authOptions)

  if (!session?.user || session.user.role !== "admin") {
    redirect("/sign-in")
  }

  return (
    <div className="container py-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Statement Grading</h1>
        <p className="text-muted-foreground">
          Review and grade student statements out of 25 points
        </p>
      </div>
      <GradingInterface />
    </div>
  )
}

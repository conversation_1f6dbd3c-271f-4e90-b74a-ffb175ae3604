"use client"

import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"
import { Bar<PERSON>hart3, Target, TrendingUp } from "lucide-react"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"

interface GradingStatsProps {
  stats: {
    totalUngraded: number
    gradedToday: number
    averageScore: number
    scoreDistribution: {
      range: string
      count: number
    }[]
  }
}

export function GradingStats({ stats }: GradingStatsProps) {
  const progressPercentage =
    stats.totalUngraded > 0
      ? (stats.gradedToday / (stats.totalUngraded + stats.gradedToday)) * 100
      : 100

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <Target className="h-4 w-4 text-muted-foreground" />
            Grading Progress
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">
                Today&apos;s Progress
              </span>
              <span className="font-medium">
                {Math.round(progressPercentage)}%
              </span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </div>

          <div className="grid grid-cols-2 gap-4 pt-2">
            <div className="space-y-1">
              <p className="text-2xl font-semibold">{stats.totalUngraded}</p>
              <p className="text-xs text-muted-foreground">Remaining</p>
            </div>
            <div className="space-y-1">
              <p className="text-2xl font-semibold">{stats.gradedToday}</p>
              <p className="text-xs text-muted-foreground">Graded Today</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
            Average Score
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-baseline gap-1">
            <span className="text-3xl font-semibold">
              {stats.averageScore.toFixed(1)}
            </span>
            <span className="text-sm text-muted-foreground">/ 25</span>
          </div>
          <Progress
            value={(stats.averageScore / 25) * 100}
            className="h-2 mt-3"
          />
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
            Score Distribution
          </CardTitle>
          <CardDescription>Number of students by score range</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-[200px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={stats.scoreDistribution}>
                <XAxis
                  dataKey="range"
                  tick={{ fontSize: 12 }}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis
                  tick={{ fontSize: 12 }}
                  tickLine={false}
                  axisLine={false}
                />
                <Tooltip
                  content={({ active, payload }) => {
                    if (active && payload && payload[0]) {
                      return (
                        <div className="rounded-lg border bg-background p-2 shadow-sm">
                          <div className="grid grid-cols-2 gap-2">
                            <span className="text-[0.70rem] uppercase text-muted-foreground">
                              Range
                            </span>
                            <span className="font-bold text-right text-muted-foreground">
                              {payload[0].payload.range}
                            </span>
                            <span className="text-[0.70rem] uppercase text-muted-foreground">
                              Count
                            </span>
                            <span className="font-bold text-right">
                              {payload[0].value}
                            </span>
                          </div>
                        </div>
                      )
                    }
                    return null
                  }}
                />
                <Bar
                  dataKey="count"
                  fill="hsl(var(--primary))"
                  radius={[4, 4, 0, 0]}
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

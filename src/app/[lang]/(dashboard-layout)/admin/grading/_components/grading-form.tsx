"use client"

import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Bar<PERSON>hart2, <PERSON>h, <PERSON>ader2, User } from "lucide-react"

import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Slider } from "@/components/ui/slider"
import { Textarea } from "@/components/ui/textarea"

const gradingFormSchema = z.object({
  score: z
    .number()
    .min(0, "Score must be at least 0")
    .max(25, "Score cannot exceed 25"),
  comments: z.string().optional(),
})

type GradingFormValues = z.infer<typeof gradingFormSchema>

interface GradingFormProps {
  statement: {
    id: string
    studentId: string
    studentName: string
    studentEmail: string
    statement: string
    coreGpa: number | null
    lrwGpa: number | null
    createdAt: string
  }
  onSave: (score: number, comments?: string) => void
  saving: boolean
}

export function GradingForm({ statement, onSave, saving }: GradingFormProps) {
  const form = useForm<GradingFormValues>({
    resolver: zodResolver(gradingFormSchema),
    defaultValues: {
      score: 0,
      comments: "",
    },
  })

  const onSubmit = (values: GradingFormValues) => {
    onSave(values.score, values.comments)
    form.reset()
  }

  return (
    <Card>
      <CardHeader>
        <div className="space-y-4">
          <div className="flex items-start justify-between">
            <div className="space-y-1">
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                {statement.studentName}
              </CardTitle>
              <CardDescription className="flex items-center gap-2">
                <Hash className="h-4 w-4" />
                ID: {statement.studentId}
              </CardDescription>
            </div>
            <div className="flex gap-2">
              {statement.coreGpa !== null && (
                <Badge variant="outline" className="flex items-center gap-1">
                  <BarChart2 className="h-3 w-3" />
                  Core GPA: {statement.coreGpa.toFixed(2)}
                </Badge>
              )}
              {statement.lrwGpa !== null && (
                <Badge variant="outline" className="flex items-center gap-1">
                  <BarChart2 className="h-3 w-3" />
                  LRW GPA: {statement.lrwGpa.toFixed(2)}
                </Badge>
              )}
            </div>
          </div>
          <Separator />
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <h3 className="font-medium text-sm text-muted-foreground">
            Statement
          </h3>
          <div className="rounded-lg bg-muted p-4">
            <p className="text-sm leading-relaxed">{statement.statement}</p>
          </div>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="score"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Score</FormLabel>
                  <div className="space-y-3">
                    <div className="flex items-center gap-4">
                      <FormControl>
                        <Slider
                          min={0}
                          max={25}
                          step={1}
                          value={[field.value || 0]}
                          onValueChange={(value) => field.onChange(value[0])}
                          className="flex-1"
                        />
                      </FormControl>
                      <div className="flex items-center gap-2">
                        <Input
                          type="number"
                          min={0}
                          max={25}
                          value={field.value || 0}
                          onChange={(e) => {
                            const value = Number(e.target.value)
                            if (value >= 0 && value <= 25) {
                              field.onChange(value)
                            }
                          }}
                          className="w-16 text-center"
                        />
                        <span className="text-sm text-muted-foreground">
                          /25
                        </span>
                      </div>
                    </div>
                    <div className="flex justify-between text-xs text-muted-foreground">
                      <span>0</span>
                      <span>5</span>
                      <span>10</span>
                      <span>15</span>
                      <span>20</span>
                      <span>25</span>
                    </div>
                  </div>
                  <FormDescription>
                    Grade the statement on a scale of 0 to 25 points
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="comments"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Comments (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Add any feedback or notes about this grading..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Optional feedback that will be saved with the grade
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end">
              <Button type="submit" disabled={saving}>
                {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Save Grade
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}

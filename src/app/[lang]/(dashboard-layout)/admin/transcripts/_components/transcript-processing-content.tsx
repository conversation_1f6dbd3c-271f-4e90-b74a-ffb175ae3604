"use client"

import { useEffe<PERSON>, useState } from "react"
import {
  CheckCircle2,
  FileText,
  Loader2,
  Search,
  Upload,
  User,
  XCircle,
} from "lucide-react"

import type { UploadedFile } from "@/components/file-upload/universal-file-upload"

import { toast } from "@/hooks/use-toast"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Progress } from "@/components/ui/progress"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { UniversalFileUpload } from "@/components/file-upload/universal-file-upload"

interface ProcessedTranscript {
  id: string
  studentName: string
  studentId: string
  studentEmail: string
  processedAt: string
  gpa: number
  totalCredits: number
  status: "success" | "failed"
  error?: string
}

interface Student {
  id: string
  name: string
  email: string
  avatar?: {
    url: string
  }
}

export function TranscriptProcessingContent() {
  const [students, setStudents] = useState<Student[]>([])
  const [processedTranscripts, setProcessedTranscripts] = useState<
    ProcessedTranscript[]
  >([])
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [isLoadingStudents, setIsLoadingStudents] = useState(true)
  const [isUploadOpen, setIsUploadOpen] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [searchQuery, setSearchQuery] = useState("")

  useEffect(() => {
    fetchStudents()
    fetchProcessedTranscripts()
  }, [])

  const fetchStudents = async () => {
    try {
      const response = await fetch("/api/admin/students")
      if (!response.ok) {
        throw new Error("Failed to fetch students")
      }
      const data = await response.json()
      setStudents(data)
    } catch (error) {
      console.error("Error fetching students:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load students",
      })
    } finally {
      setIsLoadingStudents(false)
    }
  }

  const fetchProcessedTranscripts = async () => {
    // In a real implementation, this would fetch from the database
    // For now, we'll use mock data
    const mockData: ProcessedTranscript[] = [
      {
        id: "1",
        studentName: "John Doe",
        studentId: "250123456",
        studentEmail: "<EMAIL>",
        processedAt: new Date().toISOString(),
        gpa: 3.75,
        totalCredits: 45,
        status: "success",
      },
      {
        id: "2",
        studentName: "Jane Smith",
        studentId: "250234567",
        studentEmail: "<EMAIL>",
        processedAt: new Date(Date.now() - 86400000).toISOString(),
        gpa: 3.82,
        totalCredits: 42,
        status: "success",
      },
    ]
    setProcessedTranscripts(mockData)
  }

  const handleTranscriptUpload = async (files: UploadedFile[]) => {
    if (files.length === 0 || !selectedStudent) return

    const file = files[0]
    setIsProcessing(true)
    setUploadProgress(20)

    try {
      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => Math.min(prev + 10, 90))
      }, 500)

      // Process the transcript using OCR
      const response = await fetch("/api/ocr/process-transcript", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          fileUrl: file.url,
          studentProfileId: selectedStudent.id,
        }),
      })

      clearInterval(progressInterval)
      setUploadProgress(100)

      if (!response.ok) {
        throw new Error("Failed to process transcript")
      }

      const result = await response.json()

      if (result.success) {
        toast({
          title: "Success",
          description: `Transcript processed successfully for ${selectedStudent.name}`,
        })

        // Add to processed list
        const newProcessed: ProcessedTranscript = {
          id: Date.now().toString(),
          studentName: selectedStudent.name,
          studentId: result.data.studentId,
          studentEmail: selectedStudent.email,
          processedAt: new Date().toISOString(),
          gpa: result.data.gpa,
          totalCredits: result.data.totalCredits,
          status: "success",
        }
        setProcessedTranscripts([newProcessed, ...processedTranscripts])
        setIsUploadOpen(false)
        setSelectedStudent(null)
      } else {
        throw new Error(result.error || "Processing failed")
      }
    } catch (error) {
      console.error("Transcript processing error:", error)
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to process transcript. Please try again.",
      })

      // Add to processed list with error
      const failedProcessed: ProcessedTranscript = {
        id: Date.now().toString(),
        studentName: selectedStudent.name,
        studentId: "Unknown",
        studentEmail: selectedStudent.email,
        processedAt: new Date().toISOString(),
        gpa: 0,
        totalCredits: 0,
        status: "failed",
        error: error instanceof Error ? error.message : "Unknown error",
      }
      setProcessedTranscripts([failedProcessed, ...processedTranscripts])
    } finally {
      setIsProcessing(false)
      setUploadProgress(0)
    }
  }

  const filteredStudents = students.filter(
    (student) =>
      student.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      student.email.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const stats = {
    totalProcessed: processedTranscripts.length,
    successfulProcessed: processedTranscripts.filter(
      (t) => t.status === "success"
    ).length,
    failedProcessed: processedTranscripts.filter((t) => t.status === "failed")
      .length,
    averageGPA:
      processedTranscripts
        .filter((t) => t.status === "success")
        .reduce((sum, t) => sum + t.gpa, 0) /
        processedTranscripts.filter((t) => t.status === "success").length || 0,
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">
          Transcript Processing
        </h1>
        <p className="text-muted-foreground">
          Process student transcripts using OCR to extract grade information
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Processed
            </CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalProcessed}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Successful</CardTitle>
            <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {stats.successfulProcessed}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Failed</CardTitle>
            <XCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {stats.failedProcessed}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average GPA</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.averageGPA.toFixed(2)}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Student Selection and Upload */}
      <Card>
        <CardHeader>
          <CardTitle>Process New Transcript</CardTitle>
          <CardDescription>
            Select a student and upload their transcript PDF
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" className="flex-1">
                  <User className="h-4 w-4 mr-2" />
                  {selectedStudent ? selectedStudent.name : "Select Student"}
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Select Student</DialogTitle>
                  <DialogDescription>
                    Choose a student to process their transcript
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Search className="h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search students..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  {isLoadingStudents ? (
                    <div className="space-y-2">
                      {[1, 2, 3].map((i) => (
                        <Skeleton key={i} className="h-16 w-full" />
                      ))}
                    </div>
                  ) : (
                    <div className="max-h-[400px] overflow-y-auto space-y-2">
                      {filteredStudents.map((student) => (
                        <button
                          key={student.id}
                          onClick={() => setSelectedStudent(student)}
                          className="w-full flex items-center gap-3 p-3 rounded-lg hover:bg-muted transition-colors text-left"
                        >
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={student.avatar?.url} />
                            <AvatarFallback>
                              {student.name
                                .split(" ")
                                .map((n) => n[0])
                                .join("")
                                .slice(0, 2)}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <p className="font-medium">{student.name}</p>
                            <p className="text-sm text-muted-foreground">
                              {student.email}
                            </p>
                          </div>
                          {selectedStudent?.id === student.id && (
                            <CheckCircle2 className="h-5 w-5 text-primary" />
                          )}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </DialogContent>
            </Dialog>
            <Dialog open={isUploadOpen} onOpenChange={setIsUploadOpen}>
              <DialogTrigger asChild>
                <Button disabled={!selectedStudent}>
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Transcript
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Upload Transcript</DialogTitle>
                  <DialogDescription>
                    Upload the student&apos;s official transcript PDF for
                    processing
                  </DialogDescription>
                </DialogHeader>
                {isProcessing ? (
                  <div className="space-y-4 py-8">
                    <div className="flex flex-col items-center gap-4">
                      <Loader2 className="h-8 w-8 animate-spin text-primary" />
                      <p className="text-sm text-muted-foreground">
                        Processing transcript...
                      </p>
                    </div>
                    <Progress value={uploadProgress} className="w-full" />
                  </div>
                ) : (
                  <UniversalFileUpload
                    onUpload={handleTranscriptUpload}
                    accept={{
                      "application/pdf": [".pdf"],
                    }}
                    maxSize={10 * 1024 * 1024}
                    maxFiles={1}
                  />
                )}
              </DialogContent>
            </Dialog>
          </div>
        </CardContent>
      </Card>

      {/* Processed Transcripts Table */}
      <Card>
        <CardHeader>
          <CardTitle>Processed Transcripts</CardTitle>
          <CardDescription>
            History of all processed student transcripts
          </CardDescription>
        </CardHeader>
        <CardContent>
          {processedTranscripts.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12">
              <FileText className="h-12 w-12 text-muted-foreground mb-4" />
              <p className="text-lg font-medium">
                No transcripts processed yet
              </p>
              <p className="text-muted-foreground">
                Process a student transcript to see it here
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Student</TableHead>
                  <TableHead>Student ID</TableHead>
                  <TableHead>GPA</TableHead>
                  <TableHead>Credits</TableHead>
                  <TableHead>Processed</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {processedTranscripts.map((transcript) => (
                  <TableRow key={transcript.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{transcript.studentName}</p>
                        <p className="text-sm text-muted-foreground">
                          {transcript.studentEmail}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>{transcript.studentId}</TableCell>
                    <TableCell>
                      {transcript.status === "success" ? (
                        <span className="font-medium">
                          {transcript.gpa.toFixed(2)}
                        </span>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {transcript.status === "success" ? (
                        <span>{transcript.totalCredits}</span>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {new Date(transcript.processedAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          transcript.status === "success"
                            ? "default"
                            : "destructive"
                        }
                      >
                        {transcript.status === "success" ? (
                          <>
                            <CheckCircle2 className="h-3 w-3 mr-1" />
                            Success
                          </>
                        ) : (
                          <>
                            <XCircle className="h-3 w-3 mr-1" />
                            Failed
                          </>
                        )}
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"

import { authOptions } from "@/configs/next-auth"

import { TranscriptProcessingContent } from "./_components/transcript-processing-content"

export default async function AdminTranscriptsPage() {
  const session = await getServerSession(authOptions)

  if (!session?.user || session.user.role !== "admin") {
    redirect("/sign-in")
  }

  return <TranscriptProcessingContent />
}

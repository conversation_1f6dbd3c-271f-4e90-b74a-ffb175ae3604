"use client"

import { useState } from "react"
import { <PERSON>R<PERSON>, CheckCircle, Download } from "lucide-react"

import { toast } from "@/hooks/use-toast"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

interface MatchResult {
  studentId: string
  studentName: string
  organizationId: string
  organizationName: string
  matchScore: number
  status: "pending" | "approved" | "rejected"
}

export function MatchingResults() {
  const [results] = useState<MatchResult[]>([
    {
      studentId: "S001",
      studentName: "Jane Doe",
      organizationId: "O001",
      organizationName: "Legal Aid Society",
      matchScore: 92,
      status: "pending",
    },
    {
      studentId: "S002",
      studentName: "<PERSON>",
      organizationId: "O002",
      organizationName: "Public Defenders Office",
      matchScore: 88,
      status: "pending",
    },
    // Mock data - would come from API
  ])

  const handleApproveAll = async () => {
    try {
      // TODO: Call API to approve all matches
      toast({
        title: "Matches Approved",
        description:
          "All matches have been approved and students will be notified",
      })
    } catch (_error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to approve matches",
      })
    }
  }

  const handleExport = () => {
    // TODO: Export results to CSV
    toast({
      title: "Export Started",
      description: "Downloading match results as CSV",
    })
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Match Results</CardTitle>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="mr-2 h-4 w-4" />
            Export CSV
          </Button>
          <Button size="sm" onClick={handleApproveAll}>
            <CheckCircle className="mr-2 h-4 w-4" />
            Approve All
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Student</TableHead>
              <TableHead>Organization</TableHead>
              <TableHead>Match Score</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {results.map((result) => (
              <TableRow key={`${result.studentId}-${result.organizationId}`}>
                <TableCell className="font-medium">
                  {result.studentName}
                </TableCell>
                <TableCell>{result.organizationName}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <div className="text-sm font-medium">
                      {result.matchScore}%
                    </div>
                    <div className="h-2 w-20 bg-secondary rounded-full overflow-hidden">
                      <div
                        className="h-full bg-primary"
                        style={{ width: `${result.matchScore}%` }}
                      />
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge
                    variant={
                      result.status === "approved"
                        ? "default"
                        : result.status === "rejected"
                          ? "destructive"
                          : "secondary"
                    }
                  >
                    {result.status}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <Button variant="ghost" size="sm">
                    View Details
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}

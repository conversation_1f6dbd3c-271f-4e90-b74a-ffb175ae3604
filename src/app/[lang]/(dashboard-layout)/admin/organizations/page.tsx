import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"

import { authOptions } from "@/configs/next-auth"

import { OrganizationsHeader } from "./_components/organizations-header"
import { OrganizationsTable } from "./_components/organizations-table"

export default async function AdminOrganizationsPage() {
  const session = await getServerSession(authOptions)

  if (!session?.user || session.user.role !== "admin") {
    redirect("/sign-in")
  }

  return (
    <div className="container py-6 space-y-6">
      <OrganizationsHeader />
      <OrganizationsTable />
    </div>
  )
}

"use client"

import { useState } from "react"
import { Building2, Plus, Search } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { AddOrganizationDialog } from "./add-organization-dialog"

interface OrganizationsHeaderProps {
  onSearch?: (search: string) => void
}

export function OrganizationsHeader({ onSearch }: OrganizationsHeaderProps) {
  const [searchValue, setSearchValue] = useState("")
  const [isAddOpen, setIsAddOpen] = useState(false)

  const handleSearch = (value: string) => {
    setSearchValue(value)
    onSearch?.(value)
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Building2 className="h-8 w-8" />
            Organizations
          </h1>
          <p className="text-muted-foreground">
            Manage partner organizations and their access
          </p>
        </div>
        <Button onClick={() => setIsAddOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Organization
        </Button>
      </div>

      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
        <Input
          placeholder="Search organizations..."
          value={searchValue}
          onChange={(e) => handleSearch(e.target.value)}
          className="pl-10"
        />
      </div>

      <AddOrganizationDialog
        open={isAddOpen}
        onOpenChange={setIsAddOpen}
        onSuccess={() => {
          setIsAddOpen(false)
          window.location.reload() // Simple refresh for now
        }}
      />
    </div>
  )
}

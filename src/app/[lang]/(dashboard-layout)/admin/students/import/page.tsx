import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"

import type { LocaleType } from "@/types/i18n"
import type { Metadata } from "next"

import { authOptions } from "@/configs/next-auth"
import { getDictionary } from "@/lib/get-dictionary"

import { StudentImportForm } from "./_components/student-import-form"

export const metadata: Metadata = {
  title: "Import Students",
  description: "Import student data from CSV file",
}

export default async function StudentImportPage(props: {
  params: Promise<{ lang: LocaleType }>
}) {
  const params = await props.params
  const { lang } = params
  const session = await getServerSession(authOptions)

  if (!session?.user) {
    redirect(`/${lang}/sign-in`)
  }

  if (session.user.role !== "admin") {
    redirect(`/${lang}/dashboards/${session.user.role}`)
  }

  const dictionary = await getDictionary(lang)

  return (
    <div className="container max-w-4xl py-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Import Students</h1>
        <p className="text-muted-foreground">
          Upload SA1L_deduplicated.csv file to import student data
        </p>
      </div>

      <StudentImportForm dictionary={dictionary} />
    </div>
  )
}

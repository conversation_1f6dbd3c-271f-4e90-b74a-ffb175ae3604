"use client"

import { useState } from "react"
import { redirect } from "next/navigation"
import { useSession } from "next-auth/react"

import { StudentsHeader } from "./_components/students-header"
import { StudentsTable } from "./_components/students-table"

export default function AdminStudentsPage() {
  const { data: session, status } = useSession()
  const [searchQuery, setSearchQuery] = useState("")

  if (status === "loading") {
    return <div>Loading...</div>
  }

  if (!session?.user || session.user.role !== "admin") {
    redirect("/sign-in")
  }

  return (
    <div className="container py-6 space-y-6">
      <StudentsHeader onSearch={setSearchQuery} />
      <StudentsTable searchQuery={searchQuery} />
    </div>
  )
}

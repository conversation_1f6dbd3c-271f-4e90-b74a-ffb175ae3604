"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { format } from "date-fns"
import { Check, Download, Eye, FileText, Loader2, X } from "lucide-react"

import { useToast } from "@/hooks/use-toast"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Progress } from "@/components/ui/progress"
import { Skeleton } from "@/components/ui/skeleton"
import { Textarea } from "@/components/ui/textarea"

interface MatchedStudent {
  id: string
  studentId: string
  studentName: string
  studentEmail: string
  program: string
  coreGPA: number | null
  lrwGPA: number | null
  matchScore: number
  matchDetails: Record<string, unknown>
  studentAccepted: boolean | null
  supervisorAccepted: boolean | null
  status: string
  createdAt: string
  resumeUrl?: string
  transcriptUrl?: string
  grades: Array<{
    courseCode: string
    courseName: string
    grade: string
    gradePoint: number
    credits: number
    term: string
    year: string
  }>
  statements: Array<{
    areaOfLawId: string
    statement: string
    score: number | null
  }>
  preferredResearchProjects: string[]
}

export function MatchedStudents() {
  const [students, setStudents] = useState<MatchedStudent[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedStudent, setSelectedStudent] = useState<MatchedStudent | null>(
    null
  )
  const [showRejectDialog, setShowRejectDialog] = useState(false)
  const [rejectReason, setRejectReason] = useState("")
  const [processingMatch, setProcessingMatch] = useState<string | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    fetchMatchedStudents()
  }, [])

  const fetchMatchedStudents = async () => {
    try {
      const response = await fetch("/api/faculty/matched-students")
      if (response.ok) {
        const data = await response.json()
        setStudents(data)
      }
    } catch (error) {
      console.error("Failed to fetch matched students:", error)
      toast({
        title: "Error",
        description: "Failed to load matched students",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleMatchResponse = async (matchId: string, accept: boolean) => {
    setProcessingMatch(matchId)
    try {
      const response = await fetch("/api/faculty/match-response", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          matchId,
          accept,
          reason: accept ? null : rejectReason,
        }),
      })

      if (response.ok) {
        toast({
          title: "Success",
          description: accept
            ? "Student match accepted"
            : "Student match rejected",
        })

        // Update local state
        setStudents(
          students.map((s) =>
            s.id === matchId ? { ...s, supervisorAccepted: accept } : s
          )
        )

        setShowRejectDialog(false)
        setRejectReason("")
        setSelectedStudent(null)
      } else {
        throw new Error("Failed to process match response")
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to process match response",
        variant: "destructive",
      })
    } finally {
      setProcessingMatch(null)
    }
  }

  if (isLoading) {
    return <MatchedStudentsSkeleton />
  }

  const getStatusBadge = (student: MatchedStudent) => {
    if (student.supervisorAccepted && student.studentAccepted) {
      return <Badge variant="default">Confirmed</Badge>
    }
    if (student.supervisorAccepted === false) {
      return <Badge variant="destructive">Rejected by You</Badge>
    }
    if (student.studentAccepted === false) {
      return <Badge variant="secondary">Rejected by Student</Badge>
    }
    if (student.supervisorAccepted && !student.studentAccepted) {
      return <Badge variant="outline">Awaiting Student</Badge>
    }
    return <Badge variant="outline">Pending Your Response</Badge>
  }

  return (
    <div className="space-y-4">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">
          Matched RA Students
        </h2>
        <p className="text-muted-foreground">
          Review and respond to student matches for your research projects
        </p>
      </div>

      {students.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <p className="text-muted-foreground text-center">
              No matched students yet. Matches will appear here after the
              matching process runs.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {students.map((student) => (
            <Card key={student.id}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-4">
                    <Avatar className="h-12 w-12">
                      <AvatarImage
                        src={`/api/avatars?seed=${student.studentEmail}`}
                      />
                      <AvatarFallback>
                        {student.studentName
                          .split(" ")
                          .map((n) => n[0])
                          .join("")
                          .toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <CardTitle className="text-lg">
                        {student.studentName}
                      </CardTitle>
                      <CardDescription>
                        {student.program} • Match Score:{" "}
                        {Math.round(student.matchScore)}%
                      </CardDescription>
                    </div>
                  </div>
                  {getStatusBadge(student)}
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-4">
                  <div>
                    <div className="text-sm font-medium text-muted-foreground">
                      Core GPA
                    </div>
                    <div className="text-2xl font-bold">
                      {student.coreGPA?.toFixed(2) || "N/A"}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-muted-foreground">
                      LRW GPA
                    </div>
                    <div className="text-2xl font-bold">
                      {student.lrwGPA?.toFixed(2) || "N/A"}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-muted-foreground">
                      Courses
                    </div>
                    <div className="text-2xl font-bold">
                      {student.grades.length}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-muted-foreground">
                      Matched
                    </div>
                    <div className="text-sm">
                      {format(new Date(student.createdAt), "MMM d, yyyy")}
                    </div>
                  </div>
                </div>

                <div className="flex flex-wrap gap-2">
                  {student.resumeUrl && (
                    <Button variant="outline" size="sm" asChild>
                      <a
                        href={student.resumeUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Resume
                      </a>
                    </Button>
                  )}
                  {student.transcriptUrl && (
                    <Button variant="outline" size="sm" asChild>
                      <a
                        href={student.transcriptUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <FileText className="h-4 w-4 mr-2" />
                        Transcript
                      </a>
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedStudent(student)}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    View Details
                  </Button>
                </div>

                {student.supervisorAccepted === null && (
                  <div className="flex gap-2 pt-2">
                    <Button
                      size="sm"
                      onClick={() => handleMatchResponse(student.id, true)}
                      disabled={processingMatch === student.id}
                    >
                      {processingMatch === student.id ? (
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Check className="h-4 w-4 mr-2" />
                      )}
                      Accept Match
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => {
                        setSelectedStudent(student)
                        setShowRejectDialog(true)
                      }}
                      disabled={processingMatch === student.id}
                    >
                      <X className="h-4 w-4 mr-2" />
                      Reject Match
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Student Details Dialog */}
      <Dialog
        open={!!selectedStudent && !showRejectDialog}
        onOpenChange={() => setSelectedStudent(null)}
      >
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          {selectedStudent && (
            <>
              <DialogHeader>
                <DialogTitle>{selectedStudent.studentName}</DialogTitle>
                <DialogDescription>
                  {selectedStudent.studentEmail} • {selectedStudent.program}
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-6">
                <div>
                  <h3 className="font-semibold mb-2">Academic Performance</h3>
                  <div className="grid gap-2 sm:grid-cols-2">
                    <div className="p-3 bg-muted rounded-lg">
                      <div className="text-sm text-muted-foreground">
                        Core GPA
                      </div>
                      <div className="text-xl font-bold">
                        {selectedStudent.coreGPA?.toFixed(2) || "N/A"}
                      </div>
                    </div>
                    <div className="p-3 bg-muted rounded-lg">
                      <div className="text-sm text-muted-foreground">
                        LRW GPA
                      </div>
                      <div className="text-xl font-bold">
                        {selectedStudent.lrwGPA?.toFixed(2) || "N/A"}
                      </div>
                    </div>
                  </div>
                </div>

                {selectedStudent.statements.length > 0 && (
                  <div>
                    <h3 className="font-semibold mb-2">Research Statements</h3>
                    <div className="space-y-3">
                      {selectedStudent.statements.map((statement, index) => (
                        <div key={index} className="p-3 border rounded-lg">
                          <div className="flex justify-between items-start mb-2">
                            <div className="text-sm font-medium">
                              Area {statement.areaOfLawId}
                            </div>
                            {statement.score && (
                              <Badge variant="secondary">
                                Score: {statement.score}/25
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm">{statement.statement}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div>
                  <h3 className="font-semibold mb-2">Match Details</h3>
                  <div className="p-3 bg-muted rounded-lg">
                    <div className="text-sm space-y-1">
                      <div>
                        Match Score: {Math.round(selectedStudent.matchScore)}%
                      </div>
                      <div>
                        Matched on:{" "}
                        {format(new Date(selectedStudent.createdAt), "PPP")}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>

      {/* Reject Dialog */}
      <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Student Match</DialogTitle>
            <DialogDescription>
              Please provide a reason for rejecting this match. This will help
              improve future matching.
            </DialogDescription>
          </DialogHeader>

          <Textarea
            placeholder="Enter reason for rejection..."
            value={rejectReason}
            onChange={(e) => setRejectReason(e.target.value)}
            className="min-h-[100px]"
          />

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowRejectDialog(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() =>
                selectedStudent &&
                handleMatchResponse(selectedStudent.id, false)
              }
              disabled={
                !rejectReason.trim() || processingMatch === selectedStudent?.id
              }
            >
              {processingMatch === selectedStudent?.id ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : null}
              Confirm Rejection
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

function MatchedStudentsSkeleton() {
  return (
    <div className="space-y-4">
      <div className="space-y-1">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-4 w-64" />
      </div>
      <div className="grid gap-4">
        {[...Array(2)].map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <div className="flex items-center gap-4">
                <Skeleton className="h-12 w-12 rounded-full" />
                <div className="space-y-2">
                  <Skeleton className="h-5 w-32" />
                  <Skeleton className="h-4 w-48" />
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 sm:grid-cols-4">
                {[...Array(4)].map((_, j) => (
                  <div key={j} className="space-y-1">
                    <Skeleton className="h-4 w-16" />
                    <Skeleton className="h-6 w-12" />
                  </div>
                ))}
              </div>
              <div className="flex gap-2">
                <Skeleton className="h-9 w-24" />
                <Skeleton className="h-9 w-24" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"

import { authOptions } from "@/configs/next-auth"

import { ResearchProjectForm } from "./_components/research-project-form"

export default async function NewProjectPage({
  params,
}: {
  params: Promise<{ lang: string }>
}) {
  const { lang } = await params
  const session = await getServerSession(authOptions)

  if (!session?.user || session.user.role !== "faculty") {
    redirect("/sign-in")
  }

  return (
    <div className="container py-6 max-w-4xl">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Create Research Project
          </h1>
          <p className="text-muted-foreground">
            Define a new research project for RA applications
          </p>
        </div>

        <ResearchProjectForm facultyId={session.user.id} lang={lang} />
      </div>
    </div>
  )
}

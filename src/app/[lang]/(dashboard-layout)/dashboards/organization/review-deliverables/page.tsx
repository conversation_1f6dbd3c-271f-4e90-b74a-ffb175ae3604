import { getServerSession } from "next-auth"

import type { Metadata } from "next"

import { authOptions } from "@/configs/next-auth"
import { DeliverablesService } from "@/lib/services/deliverables-service"

import { DeliverableReviewList } from "./_components/deliverable-review-list"

export const metadata: Metadata = {
  title: "Review Deliverables | Organization",
  description: "Review and provide feedback on student deliverables",
}

export default async function OrganizationReviewDeliverablesPage() {
  const session = await getServerSession(authOptions)

  if (!session?.user?.id || session.user.role !== "organization") {
    return null
  }

  // Get all deliverables that need review
  const deliverables = await DeliverablesService.getDeliverablesForReview(
    session.user.id
  )

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">
          Review Deliverables
        </h1>
        <p className="text-muted-foreground">
          Review student submissions and provide feedback
        </p>
      </div>

      <DeliverableReviewList
        deliverables={deliverables}
        reviewerId={session.user.id}
      />
    </div>
  )
}

"use client"

import { useEffect, useState } from "react"
import {
  addMonths,
  eachDayOfInterval,
  endOfMonth,
  endOfWeek,
  format,
  isSameDay,
  isSameMonth,
  isToday,
  startOfMonth,
  startOfWeek,
  subMonths,
} from "date-fns"
import { toast } from "sonner"
import { ChevronLeft, ChevronRight } from "lucide-react"

import { cn } from "@/lib/utils"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Skeleton } from "@/components/ui/skeleton"

interface Interview {
  id: string
  studentId: string
  student: {
    firstName: string
    lastName: string
    email: string
    university: string
    major: string
  }
  scheduledDate: string
  duration: number
  meetingType: "virtual" | "in-person"
  meetingLink?: string
  location?: string
  notes?: string
  status: "upcoming" | "completed" | "cancelled"
}

export function InterviewCalendar() {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [interviews, setInterviews] = useState<Interview[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedInterview, setSelectedInterview] = useState<Interview | null>(
    null
  )
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  useEffect(() => {
    fetchInterviews()
  }, [])

  const fetchInterviews = async () => {
    try {
      const response = await fetch("/api/organization/interviews")
      if (response.ok) {
        const data = await response.json()
        setInterviews(data.interviews || [])
      }
    } catch (error) {
      console.error("Failed to fetch interviews:", error)
      toast.error("Failed to load interviews")
    } finally {
      setIsLoading(false)
    }
  }

  const monthStart = startOfMonth(currentDate)
  const monthEnd = endOfMonth(monthStart)
  const startDate = startOfWeek(monthStart)
  const endDate = endOfWeek(monthEnd)

  const days = eachDayOfInterval({ start: startDate, end: endDate })

  const previousMonth = () => {
    setCurrentDate(subMonths(currentDate, 1))
  }

  const nextMonth = () => {
    setCurrentDate(addMonths(currentDate, 1))
  }

  const goToToday = () => {
    setCurrentDate(new Date())
  }

  const getInterviewsForDay = (day: Date) => {
    return interviews.filter((interview) =>
      isSameDay(new Date(interview.scheduledDate), day)
    )
  }

  const handleInterviewClick = (interview: Interview) => {
    setSelectedInterview(interview)
    setIsDialogOpen(true)
  }

  const getStatusColor = (status: Interview["status"]) => {
    switch (status) {
      case "upcoming":
        return "bg-blue-500"
      case "completed":
        return "bg-green-500"
      case "cancelled":
        return "bg-red-500"
      default:
        return "bg-gray-500"
    }
  }

  if (isLoading) {
    return <InterviewCalendarSkeleton />
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Interview Calendar</CardTitle>
              <CardDescription>
                View all your scheduled interviews in a calendar format
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={previousMonth}
                aria-label="Previous month"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={goToToday}>
                Today
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={nextMonth}
                aria-label="Next month"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="mb-4 text-center">
            <h3 className="text-xl font-semibold">
              {format(currentDate, "MMMM yyyy")}
            </h3>
          </div>

          {/* Calendar Grid */}
          <div className="grid grid-cols-7 gap-px bg-muted rounded-lg overflow-hidden">
            {/* Day Headers */}
            {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
              <div
                key={day}
                className="bg-background p-2 text-center text-sm font-medium text-muted-foreground"
              >
                {day}
              </div>
            ))}

            {/* Calendar Days */}
            {days.map((day, _dayIdx) => {
              const dayInterviews = getInterviewsForDay(day)
              const isCurrentMonth = isSameMonth(day, monthStart)

              return (
                <div
                  key={day.toString()}
                  className={cn(
                    "min-h-[100px] bg-background p-2 text-sm",
                    !isCurrentMonth && "text-muted-foreground bg-muted/30",
                    isToday(day) && "bg-primary/5"
                  )}
                >
                  <div className="font-medium mb-1">
                    {format(day, "d")}
                    {isToday(day) && (
                      <Badge variant="outline" className="ml-2 text-xs">
                        Today
                      </Badge>
                    )}
                  </div>

                  {/* Interview Events */}
                  <div className="space-y-1">
                    {dayInterviews.slice(0, 3).map((interview) => (
                      <button
                        key={interview.id}
                        onClick={() => handleInterviewClick(interview)}
                        className={cn(
                          "w-full text-left p-1 rounded text-xs text-white truncate",
                          getStatusColor(interview.status),
                          "hover:opacity-90 transition-opacity"
                        )}
                      >
                        {format(new Date(interview.scheduledDate), "HH:mm")} -{" "}
                        {interview.student.firstName}
                      </button>
                    ))}
                    {dayInterviews.length > 3 && (
                      <button
                        onClick={() => {
                          // You could show all interviews for this day
                          toast.info(
                            `${dayInterviews.length - 3} more interview(s)`
                          )
                        }}
                        className="text-xs text-muted-foreground hover:text-foreground"
                      >
                        +{dayInterviews.length - 3} more
                      </button>
                    )}
                  </div>
                </div>
              )
            })}
          </div>

          {/* Legend */}
          <div className="mt-4 flex items-center gap-4 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded bg-blue-500" />
              <span>Upcoming</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded bg-green-500" />
              <span>Completed</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded bg-red-500" />
              <span>Cancelled</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Interview Details Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Interview Details</DialogTitle>
            <DialogDescription>
              Interview with {selectedInterview?.student.firstName}{" "}
              {selectedInterview?.student.lastName}
            </DialogDescription>
          </DialogHeader>
          {selectedInterview && (
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-1">Student Information</h4>
                <p className="text-sm text-muted-foreground">
                  {selectedInterview.student.email}
                </p>
                <p className="text-sm text-muted-foreground">
                  {selectedInterview.student.university} -{" "}
                  {selectedInterview.student.major}
                </p>
              </div>

              <div>
                <h4 className="font-medium mb-1">Interview Details</h4>
                <p className="text-sm text-muted-foreground">
                  Date:{" "}
                  {format(new Date(selectedInterview.scheduledDate), "PPP")}
                </p>
                <p className="text-sm text-muted-foreground">
                  Time: {format(new Date(selectedInterview.scheduledDate), "p")}
                </p>
                <p className="text-sm text-muted-foreground">
                  Duration: {selectedInterview.duration} minutes
                </p>
                <p className="text-sm text-muted-foreground">
                  Type:{" "}
                  {selectedInterview.meetingType === "virtual"
                    ? "Virtual"
                    : "In-Person"}
                </p>
                {selectedInterview.meetingLink && (
                  <p className="text-sm">
                    <a
                      href={selectedInterview.meetingLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:underline"
                    >
                      Join Meeting
                    </a>
                  </p>
                )}
                {selectedInterview.location && (
                  <p className="text-sm text-muted-foreground">
                    Location: {selectedInterview.location}
                  </p>
                )}
              </div>

              {selectedInterview.notes && (
                <div>
                  <h4 className="font-medium mb-1">Notes</h4>
                  <p className="text-sm text-muted-foreground">
                    {selectedInterview.notes}
                  </p>
                </div>
              )}

              <div>
                <Badge
                  variant={
                    selectedInterview.status === "upcoming"
                      ? "default"
                      : selectedInterview.status === "completed"
                        ? "secondary"
                        : "destructive"
                  }
                >
                  {selectedInterview.status}
                </Badge>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  )
}

function InterviewCalendarSkeleton() {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-4 w-64" />
          </div>
          <div className="flex gap-2">
            <Skeleton className="h-9 w-9" />
            <Skeleton className="h-9 w-16" />
            <Skeleton className="h-9 w-9" />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Skeleton className="h-6 w-32 mx-auto mb-4" />
        <div className="grid grid-cols-7 gap-px">
          {[...Array(49)].map((_, i) => (
            <Skeleton key={i} className="h-24" />
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

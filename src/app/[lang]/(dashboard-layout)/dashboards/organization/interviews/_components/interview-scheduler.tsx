"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { zod<PERSON>esol<PERSON> } from "@hookform/resolvers/zod"
import { format } from "date-fns"
import { useForm } from "react-hook-form"
import { toast } from "sonner"
import { z } from "zod"
import { CalendarIcon, Clock, Link, MapPin, User, Video } from "lucide-react"

import { cn } from "@/lib/utils"

import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"

const interviewSchema = z.object({
  studentId: z.string().min(1, "Please select a student"),
  date: z.date({
    required_error: "Please select a date",
  }),
  time: z.string().min(1, "Please select a time"),
  duration: z.enum(["30", "45", "60"]),
  meetingType: z.enum(["virtual", "in-person"]),
  meetingLink: z.string().optional(),
  location: z.string().optional(),
  notes: z.string().optional(),
})

type InterviewFormData = z.infer<typeof interviewSchema>

interface MatchedStudent {
  id: string
  firstName: string
  lastName: string
  email: string
  university: string
  major: string
}

export function InterviewScheduler() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [matchedStudents, setMatchedStudents] = useState<MatchedStudent[]>([])
  const [loadingStudents, setLoadingStudents] = useState(true)

  const form = useForm<InterviewFormData>({
    resolver: zodResolver(interviewSchema),
    defaultValues: {
      duration: "45",
      meetingType: "virtual",
      notes: "",
    },
  })

  // Fetch matched students on component mount
  useEffect(() => {
    fetchMatchedStudents()
  }, [])

  const fetchMatchedStudents = async () => {
    try {
      const response = await fetch("/api/organization/matched-students")
      if (response.ok) {
        const data = await response.json()
        setMatchedStudents(data.students || [])
      }
    } catch (error) {
      console.error("Failed to fetch matched students:", error)
      toast.error("Failed to load matched students")
    } finally {
      setLoadingStudents(false)
    }
  }

  const onSubmit = async (data: InterviewFormData) => {
    setIsLoading(true)
    try {
      const response = await fetch("/api/organization/interviews", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          studentId: data.studentId,
          scheduledDate: new Date(
            `${format(data.date, "yyyy-MM-dd")}T${data.time}`
          ).toISOString(),
          duration: parseInt(data.duration),
          meetingType: data.meetingType,
          meetingLink: data.meetingLink,
          location: data.location,
          notes: data.notes,
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to schedule interview")
      }

      toast.success("Interview scheduled successfully!")
      form.reset()
      router.refresh()
    } catch (error) {
      console.error("Error scheduling interview:", error)
      toast.error("Failed to schedule interview. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleOutlookIntegration = () => {
    toast.info("Outlook integration coming soon!")
    // Mock Outlook integration - in production, this would open Outlook or use Microsoft Graph API
  }

  const meetingType = form.watch("meetingType")

  return (
    <Card>
      <CardHeader>
        <CardTitle>Schedule New Interview</CardTitle>
        <CardDescription>
          Schedule an interview with a matched student
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Student Selection */}
            <FormField
              control={form.control}
              name="studentId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Select Student</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={loadingStudents}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a student" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {matchedStudents.length === 0 ? (
                        <SelectItem value="no-students" disabled>
                          No matched students available
                        </SelectItem>
                      ) : (
                        matchedStudents.map((student) => (
                          <SelectItem key={student.id} value={student.id}>
                            <div className="flex items-center gap-2">
                              <User className="h-4 w-4" />
                              <span>
                                {student.firstName} {student.lastName}
                              </span>
                              <span className="text-muted-foreground text-sm">
                                ({student.university} - {student.major})
                              </span>
                            </div>
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Date and Time */}
            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full justify-start text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date < new Date() || date < new Date("1900-01-01")
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="time"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Time</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select time" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {Array.from({ length: 19 }, (_, i) => {
                          const hour = Math.floor(i / 2) + 8
                          const minute = i % 2 === 0 ? "00" : "30"
                          const time = `${hour.toString().padStart(2, "0")}:${minute}`
                          return (
                            <SelectItem key={time} value={time}>
                              {time}
                            </SelectItem>
                          )
                        })}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Duration and Meeting Type */}
            <div className="grid gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="duration"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Duration</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="30">
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4" />
                            30 minutes
                          </div>
                        </SelectItem>
                        <SelectItem value="45">
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4" />
                            45 minutes
                          </div>
                        </SelectItem>
                        <SelectItem value="60">
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4" />
                            60 minutes
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="meetingType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Meeting Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="virtual">
                          <div className="flex items-center gap-2">
                            <Video className="h-4 w-4" />
                            Virtual
                          </div>
                        </SelectItem>
                        <SelectItem value="in-person">
                          <div className="flex items-center gap-2">
                            <MapPin className="h-4 w-4" />
                            In-Person
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Meeting Link or Location */}
            {meetingType === "virtual" ? (
              <FormField
                control={form.control}
                name="meetingLink"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Meeting Link</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Link className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="https://teams.microsoft.com/..."
                          className="pl-9"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormDescription>
                      Enter the video conference link
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            ) : (
              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Office address or room number"
                          className="pl-9"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormDescription>
                      Enter the meeting location
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Additional notes or instructions for the interview..."
                      className="resize-none"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Optional notes for the interview
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Actions */}
            <div className="flex gap-4 justify-between">
              <Button
                type="button"
                variant="outline"
                onClick={handleOutlookIntegration}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                Add to Outlook
              </Button>
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => form.reset()}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? "Scheduling..." : "Schedule Interview"}
                </Button>
              </div>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}

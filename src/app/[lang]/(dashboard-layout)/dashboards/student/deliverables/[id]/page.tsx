import { notFound, redirect } from "next/navigation"
import { db } from "@/drizzle/db"
import {
  deliverableDrafts,
  deliverableRevisions,
  deliverables,
} from "@/drizzle/schema"
import { and, eq } from "drizzle-orm"
import { getServerSession } from "next-auth"

import type { LocaleType } from "@/types/i18n"

import { authOptions } from "@/configs/next-auth"
import { getDictionary } from "@/lib/get-dictionary"

import { DeliverableSubmissionForm } from "./_components/deliverable-submission-form"

export default async function StudentDeliverableViewPage({
  params,
}: {
  params: Promise<{ lang: string; id: string }>
}) {
  const { lang, id } = await params
  const session = await getServerSession(authOptions)

  if (!session?.user || session.user.role !== "student") {
    redirect(`/${lang}/sign-in`)
  }

  const dictionary = await getDictionary(lang as LocaleType)

  // Get the deliverable with template and revisions
  const deliverable = await db.query.deliverables.findFirst({
    where: and(
      eq(deliverables.id, id),
      eq(deliverables.studentId, session.user.id)
    ),
    with: {
      template: true,
      revisions: {
        orderBy: (revisions, { desc }) => [desc(revisions.version)],
      },
      faculty: {
        columns: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
        },
      },
      organization: {
        columns: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
        },
      },
    },
  })

  if (!deliverable) {
    notFound()
  }

  // Get any existing draft
  const draft = await db.query.deliverableDrafts.findFirst({
    where: and(
      eq(deliverableDrafts.deliverableId, id),
      eq(deliverableDrafts.userId, session.user.id)
    ),
  })

  return (
    <div className="mx-auto max-w-4xl space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">
          {deliverable.title}
        </h1>
        <p className="text-muted-foreground">
          Due: {new Date(deliverable.dueDate).toLocaleDateString()}
        </p>
      </div>

      <DeliverableSubmissionForm
        deliverable={deliverable}
        draft={draft}
        userId={session.user.id}
      />
    </div>
  )
}

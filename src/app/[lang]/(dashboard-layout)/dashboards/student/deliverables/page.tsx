import { redirect } from "next/navigation"
import { getServerSession } from "next-auth"

import { authOptions } from "@/configs/next-auth"

import { DeliverablesContent } from "./_components/deliverables-content"

export default async function StudentDeliverablesPage({
  params,
}: {
  params: Promise<{ lang: string }>
}) {
  const { lang } = await params
  const session = await getServerSession(authOptions)

  if (!session?.user || session.user.role !== "student") {
    redirect(`/${lang}/sign-in`)
  }

  return <DeliverablesContent />
}

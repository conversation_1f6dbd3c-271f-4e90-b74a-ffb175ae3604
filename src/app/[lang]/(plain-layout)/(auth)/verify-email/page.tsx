import type { LocaleType } from "@/types/i18n"
import type { Metadata } from "next"

import { getDictionary } from "@/lib/get-dictionary"

import { VerifyEmail } from "@/components/auth/verify-email"

// Define metadata for the page
// More info: https://nextjs.org/docs/app/building-your-application/optimizing/metadata
export const metadata: Metadata = {
  title: "Verify Email",
}

export default async function VerifyEmailPage(props: {
  params: Promise<{ lang: LocaleType }>
}) {
  const params = await props.params
  const dictionary = await getDictionary(params.lang)

  return <VerifyEmail dictionary={dictionary} />
}

import { NextResponse } from "next/server"
import { db } from "@/drizzle/db"
import { documents } from "@/drizzle/schema"
import { eq } from "drizzle-orm"
import { getServerSession } from "next-auth"

import { authOptions } from "@/configs/next-auth"
import { AuditService } from "@/lib/audit-service"
import { WatermarkService } from "@/lib/services/watermark-service"

interface RouteParams {
  params: Promise<{
    id: string
  }>
}

/**
 * GET /api/documents/secure/[id]
 * Serves a watermarked document with security features
 */
export async function GET(request: Request, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorised" }, { status: 401 })
    }

    const { id } = await params

    // Get document from database
    const document = await db.query.documents.findFirst({
      where: eq(documents.id, id),
    })

    if (!document) {
      return NextResponse.json({ error: "Document not found" }, { status: 404 })
    }

    // Check access permissions based on user role
    const hasAccess = await checkDocumentAccess(
      document,
      session.user.id,
      session.user.role
    )

    if (!hasAccess) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    // Get client IP for tracking
    const forwarded = request.headers.get("x-forwarded-for")
    const ip = forwarded ? forwarded.split(",")[0] : "unknown"

    // Log document access
    await AuditService.log({
      userId: session.user.id,
      action: "document.accessed",
      resourceType: "document",
      resourceId: document.id,
      metadata: {
        documentName: document.name,
        documentType: document.type,
        ipAddress: ip,
      },
    })

    // Fetch the document from storage (S3 or local)
    const response = await fetch(document.url)
    if (!response.ok) {
      throw new Error("Failed to fetch document")
    }

    const documentBuffer = Buffer.from(await response.arrayBuffer())

    // Apply watermark and security features for PDFs
    if (document.type === "application/pdf" || document.name.endsWith(".pdf")) {
      const protectedPdf = await WatermarkService.protectDocument(
        documentBuffer,
        {
          userId: session.user.id,
          userName: session.user.name || session.user.email,
          email: session.user.email,
          role: session.user.role,
          ipAddress: ip,
          sessionId: session.user.id, // Use user ID as session ID for now
        }
      )

      return new NextResponse(protectedPdf, {
        headers: {
          "Content-Type": "application/pdf",
          "Content-Disposition": `inline; filename="${document.name}"`,
          "Cache-Control": "no-store, no-cache, must-revalidate",
          "X-Content-Type-Options": "nosniff",
          "X-Frame-Options": "DENY",
        },
      })
    }

    // For non-PDF documents, return as-is with security headers
    return new NextResponse(documentBuffer, {
      headers: {
        "Content-Type": document.type || "application/octet-stream",
        "Content-Disposition": `inline; filename="${document.name}"`,
        "Cache-Control": "no-store, no-cache, must-revalidate",
        "X-Content-Type-Options": "nosniff",
        "X-Frame-Options": "DENY",
      },
    })
  } catch (error) {
    console.error("Document access error:", error)
    return NextResponse.json(
      { error: "Failed to access document" },
      { status: 500 }
    )
  }
}

/**
 * Check if user has access to document
 */
async function checkDocumentAccess(
  document: typeof documents.$inferSelect,
  userId: string,
  userRole: string
): Promise<boolean> {
  // Admins can access all documents
  if (userRole === "admin") {
    return true
  }

  // Check if user owns the document
  if (document.uploadedBy === userId) {
    return true
  }

  // Check if document is associated with user's profile
  // This would need to be expanded based on your actual access control logic
  if (document.studentProfileId) {
    const profile = await db.query.studentProfiles.findFirst({
      where: (profiles, { eq }) => eq(profiles.id, document.studentProfileId!),
    })
    if (profile?.userId === userId) {
      return true
    }
  }

  // Faculty can access documents for their students
  if (userRole === "faculty" && document.studentProfileId) {
    // Check if faculty supervises this student
    const match = await db.query.matches.findFirst({
      where: (matches, { and, eq }) =>
        and(
          eq(matches.studentId, document.studentProfileId!),
          eq(matches.facultyId, userId),
          eq(matches.status, "approved")
        ),
    })
    if (match) {
      return true
    }
  }

  // Organizations can access documents for their students
  if (userRole === "organization" && document.studentProfileId) {
    const match = await db.query.matches.findFirst({
      where: (matches, { and, eq }) =>
        and(
          eq(matches.studentId, document.studentProfileId!),
          eq(matches.organizationId, userId),
          eq(matches.status, "approved")
        ),
    })
    if (match) {
      return true
    }
  }

  return false
}

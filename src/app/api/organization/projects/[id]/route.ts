import { NextResponse } from "next/server"
import { db } from "@/drizzle/db"
import { projects } from "@/drizzle/schema"
import { eq } from "drizzle-orm"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"

interface Params {
  params: Promise<{ id: string }>
}

/**
 * Get a single project by ID
 */
export async function GET(_request: NextRequest, { params }: Params) {
  try {
    const session = await getServerSession(authOptions)
    const { id } = await params

    if (!session?.user?.id || session.user.role !== "organization") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const project = await db.query.projects.findFirst({
      where: eq(projects.id, id),
      with: {
        students: {
          with: {
            student: true,
          },
        },
      },
    })

    if (!project) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 })
    }

    // Check if the project belongs to the organization
    if (project.organizationId !== session.user.id) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Transform the project data
    const transformedProject = {
      id: project.id,
      name: project.title,
      description: project.description,
      status: project.status,
      startDate: project.startDate,
      endDate: project.endDate,
      students:
        project.students?.map((ps) => ({
          id: ps.student.id,
          name: ps.student.name || "",
          email: ps.student.email || "",
        })) || [],
      tasks: [], // TODO: Add tasks table when needed
      createdAt: project.createdAt,
      updatedAt: project.updatedAt,
    }

    return NextResponse.json(transformedProject)
  } catch (error) {
    console.error("Project fetch error:", error)
    return NextResponse.json(
      { error: "Failed to fetch project" },
      { status: 500 }
    )
  }
}

/**
 * Update a project
 */
export async function PATCH(request: NextRequest, { params }: Params) {
  try {
    const session = await getServerSession(authOptions)
    const { id } = await params

    if (!session?.user?.id || session.user.role !== "organization") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()

    // Check if the project belongs to the organization
    const existingProject = await db.query.projects.findFirst({
      where: eq(projects.id, id),
    })

    if (!existingProject) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 })
    }

    if (existingProject.organizationId !== session.user.id) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 })
    }

    // Prepare update data
    const updateData: Partial<{
      title: string
      description: string | null
      status: string
      startDate: Date | null
      endDate: Date | null
      updatedAt: Date
    }> = {}
    if (body.name !== undefined) updateData.title = body.name
    if (body.description !== undefined)
      updateData.description = body.description
    if (body.status !== undefined) updateData.status = body.status
    if (body.startDate !== undefined)
      updateData.startDate = new Date(body.startDate)
    if (body.endDate !== undefined)
      updateData.endDate = body.endDate ? new Date(body.endDate) : null
    updateData.updatedAt = new Date()

    // Update the project
    const [updatedProject] = await db
      .update(projects)
      .set(updateData)
      .where(eq(projects.id, id))
      .returning()

    return NextResponse.json({
      success: true,
      project: {
        id: updatedProject.id,
        name: updatedProject.title,
        description: updatedProject.description,
        status: updatedProject.status,
        startDate: updatedProject.startDate,
        endDate: updatedProject.endDate,
        createdAt: updatedProject.createdAt,
        updatedAt: updatedProject.updatedAt,
      },
    })
  } catch (error) {
    console.error("Project update error:", error)
    return NextResponse.json(
      { error: "Failed to update project" },
      { status: 500 }
    )
  }
}

import { NextResponse } from "next/server"
import { db } from "@/drizzle/db"
import { interviews, projects } from "@/drizzle/schema"
import { and, eq, sql } from "drizzle-orm"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"

/**
 * Get organization dashboard statistics
 */
export async function GET(_request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id || session.user.role !== "organization") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get all projects for the organization
    const organizationProjects = await db.query.projects.findMany({
      where: eq(projects.organizationId, session.user.id),
      with: {
        students: true,
      },
    })

    // Calculate statistics
    const totalProjects = organizationProjects.length
    const activeProjects = organizationProjects.filter(
      (project) => project.status === "active"
    ).length

    // Get unique students across all projects
    const allStudentIds = new Set<string>()
    organizationProjects.forEach((project) => {
      if (project.students) {
        project.students.forEach((projectStudent) => {
          allStudentIds.add(projectStudent.studentId)
        })
      }
    })

    // Get pending interviews count for the organization
    const pendingInterviewsResult = await db
      .select({ count: sql<number>`count(*)::int` })
      .from(interviews)
      .where(
        and(
          eq(interviews.organizationId, session.user.id),
          eq(interviews.status, "scheduled")
        )
      )

    const pendingInterviews = pendingInterviewsResult[0]?.count || 0

    const stats = {
      totalProjects,
      activeProjects,
      totalStudents: allStudentIds.size,
      pendingInterviews,
    }

    return NextResponse.json(stats)
  } catch (_error) {
    console.error("Organization stats error:", _error)
    return NextResponse.json(
      { error: "Failed to fetch statistics" },
      { status: 500 }
    )
  }
}

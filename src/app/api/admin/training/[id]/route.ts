import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { z } from "zod"

import type { NextRequest } from "next/server"

import { authOptions as authConfig } from "@/configs/next-auth"
import { TrainingService } from "@/lib/services/training-service"

const updateTrainingModuleSchema = z.object({
  title: z.string().min(1).max(255).optional(),
  description: z.string().min(1).optional(),
  content: z.string().optional(),
  contentType: z.enum(["internal", "external_url", "video"]).optional(),
  externalUrl: z.string().url().optional(),
  isRequired: z.boolean().optional(),
  minimumScore: z.number().min(0).max(100).optional(),
  durationMinutes: z.number().min(1).optional(),
  validFromDate: z.string().datetime().optional(),
  validUntilDate: z.string().datetime().optional(),
  targetRoles: z.array(z.string()).min(1).optional(),
  isActive: z.boolean().optional(),
  displayOrder: z.number().min(0).optional(),
})

/**
 * GET /api/admin/training/[id]
 * Get specific training module (admin only)
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authConfig)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    if (session.user.role !== "admin") {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 403 }
      )
    }

    // For admin, we can get the module directly without user progress
    const trainingData = await TrainingService.getTrainingModuleWithProgress(
      params.id,
      session.user.id // This won't affect the module data, just satisfies the interface
    )

    return NextResponse.json({
      success: true,
      data: trainingData.module,
    })
  } catch (error) {
    console.error("Failed to get training module:", error)
    return NextResponse.json(
      {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to get training module",
      },
      { status: 500 }
    )
  }
}

/**
 * PUT /api/admin/training/[id]
 * Update a training module (admin only)
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authConfig)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    if (session.user.role !== "admin") {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const validatedData = updateTrainingModuleSchema.parse(body)

    // Convert date strings to Date objects if provided
    const updateData = {
      ...validatedData,
      validFromDate: validatedData.validFromDate
        ? new Date(validatedData.validFromDate)
        : undefined,
      validUntilDate: validatedData.validUntilDate
        ? new Date(validatedData.validUntilDate)
        : undefined,
    }

    const module = await TrainingService.updateTrainingModule(
      params.id,
      updateData
    )

    return NextResponse.json({
      success: true,
      data: module,
      message: "Training module updated successfully",
    })
  } catch (error) {
    console.error("Failed to update training module:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid data",
          details: error.errors,
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to update training module",
      },
      { status: 500 }
    )
  }
}

/**
 * DELETE /api/admin/training/[id]
 * Delete a training module (admin only)
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authConfig)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    if (session.user.role !== "admin") {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 403 }
      )
    }

    await TrainingService.deleteTrainingModule(params.id)

    return NextResponse.json({
      success: true,
      message: "Training module deleted successfully",
    })
  } catch (error) {
    console.error("Failed to delete training module:", error)
    return NextResponse.json(
      {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to delete training module",
      },
      { status: 500 }
    )
  }
}

import { NextResponse } from "next/server"
import { db } from "@/drizzle/db"
import { studentProfiles } from "@/drizzle/schema"
import { eq } from "drizzle-orm"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"

interface Params {
  params: Promise<{ studentId: string }>
}

/**
 * Update student's statement score
 */
export async function PATCH(request: NextRequest, { params }: Params) {
  try {
    const { studentId } = await params
    const session = await getServerSession(authOptions)

    if (!session?.user?.id || session.user.role !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { score, comments: _comments } = body

    // Validate score
    if (typeof score !== "number" || score < 0 || score > 25) {
      return NextResponse.json(
        { error: "Score must be a number between 0 and 25" },
        { status: 400 }
      )
    }

    // Update the student profile with the score
    const result = await db
      .update(studentProfiles)
      .set({
        statementScore: score,
        updatedAt: new Date(),
        // You could add a comments field to the schema if needed
        // statementComments: comments,
      })
      .where(eq(studentProfiles.id, studentId))
      .returning()

    if (!result.length) {
      return NextResponse.json(
        { error: "Student profile not found" },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      studentId,
      score,
      message: "Statement graded successfully",
    })
  } catch (error) {
    console.error("Statement grading error:", error)
    return NextResponse.json(
      { error: "Failed to grade statement" },
      { status: 500 }
    )
  }
}

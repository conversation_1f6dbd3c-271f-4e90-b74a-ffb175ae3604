import { NextResponse } from "next/server"
import { db } from "@/drizzle/db"
import { facultyProfiles, matches, users } from "@/drizzle/schema"
import { and, count, desc, eq } from "drizzle-orm"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"

export async function GET(_request: NextRequest) {
  const session = await getServerSession(authOptions)

  if (!session || session.user.role !== "admin") {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  // Get all faculty members with their profiles
  const facultyMembers = await db
    .select({
      id: users.id,
      name: users.name,
      email: users.email,
      createdAt: users.createdAt,
      department: facultyProfiles.department,
      expertise: facultyProfiles.expertise,
      canSuperviseStudents: facultyProfiles.canSuperviseStudents,
      autoMatchEnabled: facultyProfiles.autoMatchEnabled,
      maxStudents: facultyProfiles.maxStudents,
      researchProjects: facultyProfiles.researchProjects,
    })
    .from(users)
    .leftJoin(facultyProfiles, eq(users.id, facultyProfiles.userId))
    .where(eq(users.role, "faculty"))
    .orderBy(desc(users.createdAt))

  // Get current student counts for each faculty
  const facultyWithCounts = await Promise.all(
    facultyMembers.map(async (member) => {
      const [studentCount] = await db
        .select({ count: count() })
        .from(matches)
        .where(
          and(
            eq(matches.facultyId, member.id),
            eq(matches.matchType, "faculty"),
            eq(matches.studentAccepted, true),
            eq(matches.supervisorAccepted, true)
          )
        )

      return {
        ...member,
        currentStudents: studentCount?.count || 0,
      }
    })
  )

  return NextResponse.json(facultyWithCounts)
}

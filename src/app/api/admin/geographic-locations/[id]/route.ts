import { NextResponse } from "next/server"
import { db } from "@/drizzle/db"
import { geographicLocations } from "@/drizzle/schema"
import { eq } from "drizzle-orm"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"

interface Params {
  params: Promise<{ id: string }>
}

export async function GET(request: NextRequest, { params }: Params) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== "admin") {
      return NextResponse.json({ error: "Unauthorised" }, { status: 401 })
    }

    const { id } = await params
    const locationId = parseInt(id)

    if (isNaN(locationId)) {
      return NextResponse.json(
        { error: "Invalid location ID" },
        { status: 400 }
      )
    }

    const location = await db.query.geographicLocations.findFirst({
      where: eq(geographicLocations.id, locationId),
    })

    if (!location) {
      return NextResponse.json({ error: "Location not found" }, { status: 404 })
    }

    return NextResponse.json(location)
  } catch (error) {
    console.error("Error fetching geographic location:", error)
    return NextResponse.json(
      { error: "Failed to fetch geographic location" },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest, { params }: Params) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== "admin") {
      return NextResponse.json({ error: "Unauthorised" }, { status: 401 })
    }

    const { id } = await params
    const locationId = parseInt(id)

    if (isNaN(locationId)) {
      return NextResponse.json(
        { error: "Invalid location ID" },
        { status: 400 }
      )
    }

    const body = await request.json()
    const { name, province, isActive } = body

    const [updated] = await db
      .update(geographicLocations)
      .set({
        name,
        province,
        isActive,
        updatedAt: new Date(),
      })
      .where(eq(geographicLocations.id, locationId))
      .returning()

    if (!updated) {
      return NextResponse.json({ error: "Location not found" }, { status: 404 })
    }

    return NextResponse.json(updated)
  } catch (error) {
    console.error("Error updating geographic location:", error)
    return NextResponse.json(
      { error: "Failed to update geographic location" },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest, { params }: Params) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== "admin") {
      return NextResponse.json({ error: "Unauthorised" }, { status: 401 })
    }

    const { id } = await params
    const locationId = parseInt(id)

    if (isNaN(locationId)) {
      return NextResponse.json(
        { error: "Invalid location ID" },
        { status: 400 }
      )
    }

    const [deleted] = await db
      .delete(geographicLocations)
      .where(eq(geographicLocations.id, locationId))
      .returning()

    if (!deleted) {
      return NextResponse.json({ error: "Location not found" }, { status: 404 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting geographic location:", error)
    return NextResponse.json(
      { error: "Failed to delete geographic location" },
      { status: 500 }
    )
  }
}

import { NextResponse } from "next/server"
import { db } from "@/drizzle/db"
import { files, studentProfiles, users } from "@/drizzle/schema"
import { eq } from "drizzle-orm"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"

export async function GET(_request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id || session.user.role !== "student") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Fetch user with student profile
    const userWithProfile = await db.query.users.findFirst({
      where: eq(users.id, session.user.id),
      with: {
        studentProfile: true,
        files: {
          where: eq(files.purpose, "avatar"),
          limit: 1,
        },
      },
    })

    if (!userWithProfile) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    const studentProfile = userWithProfile.studentProfile
    const avatarFile = userWithProfile.files?.[0]

    // Parse name from user record
    const nameParts = userWithProfile.name?.split(" ") || []
    const firstName = nameParts[0] || ""
    const lastName = nameParts.slice(1).join(" ") || ""

    return NextResponse.json({
      firstName,
      lastName,
      email: userWithProfile.email,
      major: studentProfile?.major,
      academicYear: studentProfile?.academicYear,
      gpa: studentProfile?.gpa ? parseFloat(studentProfile.gpa) : undefined,
      skills: studentProfile?.skills || [],
      interests: studentProfile?.interests,
      availability: studentProfile?.availability,
      avatarId: avatarFile?.id,
      avatarUrl: avatarFile?.url,
    })
  } catch (error) {
    console.error("Profile fetch error:", error)
    return NextResponse.json(
      { error: "Failed to fetch profile data" },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id || session.user.role !== "student") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const formData = await request.formData()

    // Get user record
    const user = await db.query.users.findFirst({
      where: eq(users.id, session.user.id),
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Handle avatar upload if provided
    let avatarId: string | undefined
    const avatarFile = formData.get("avatar") as File
    if (avatarFile) {
      const fileExt = avatarFile.name.split(".").pop()
      const filename = `avatar-${user.id}-${Date.now()}.${fileExt}`

      const [uploadedAvatar] = await db
        .insert(files)
        .values({
          filename,
          originalName: avatarFile.name,
          mimeType: avatarFile.type,
          size: avatarFile.size,
          url: `https://placeholder-bucket.s3.amazonaws.com/${filename}`,
          s3Key: `avatars/${user.id}/${filename}`,
          uploadedBy: user.id,
          purpose: "avatar",
        })
        .returning()

      avatarId = uploadedAvatar.id
    }

    // Update user data
    const firstName = formData.get("firstName") as string
    const lastName = formData.get("lastName") as string
    const fullName = `${firstName} ${lastName}`

    await db
      .update(users)
      .set({
        name: fullName,
        avatar: avatarId,
      })
      .where(eq(users.id, user.id))

    // Update or create student profile
    const existingProfile = await db.query.studentProfiles.findFirst({
      where: eq(studentProfiles.userId, user.id),
    })

    const profileData = {
      userId: user.id,
      major: formData.get("major") as string,
      academicYear: formData.get("academicYear") as string,
      gpa: formData.get("gpa") ? (formData.get("gpa") as string) : null,
      skills: formData.get("skills")
        ? JSON.parse(formData.get("skills") as string)
        : [],
      interests: formData.get("interests") as string,
      availability: formData.get("availability") as string,
    }

    if (existingProfile) {
      await db
        .update(studentProfiles)
        .set(profileData)
        .where(eq(studentProfiles.id, existingProfile.id))
    } else {
      await db.insert(studentProfiles).values(profileData)
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Profile update error:", error)
    return NextResponse.json(
      { error: "Failed to update profile" },
      { status: 500 }
    )
  }
}

import { NextResponse } from "next/server"
import { db } from "@/drizzle/db"
import { facultyProfiles, users } from "@/drizzle/schema"
import { and, eq, isNotNull } from "drizzle-orm"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"

export async function GET(_request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== "student") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Fetch faculty profiles with research projects
    const facultyWithProjects = await db
      .select({
        userId: facultyProfiles.userId,
        facultyName: users.name,
        researchProjects: facultyProfiles.researchProjects,
        canSuperviseStudents: facultyProfiles.canSuperviseStudents,
      })
      .from(facultyProfiles)
      .innerJoin(users, eq(facultyProfiles.userId, users.id))
      .where(
        and(
          eq(users.role, "faculty"),
          eq(facultyProfiles.canSuperviseStudents, true),
          isNotNull(facultyProfiles.researchProjects)
        )
      )
      .orderBy(users.name)

    // Transform to match the expected format - flatten the research projects array
    const projects = facultyWithProjects.flatMap((fp) => {
      const projectsData = fp.researchProjects as Array<{
        title: string
        description: string
        requirements?: string
      }> | null

      if (!projectsData || !Array.isArray(projectsData)) {
        return []
      }

      return projectsData.map((project, index) => ({
        id: `${fp.userId}-${index}`,
        name: project.title || "Untitled Project",
        facultyName: fp.facultyName || "Unknown Faculty",
        description: project.description || "No description available",
        requirements: project.requirements,
      }))
    })

    return NextResponse.json(projects)
  } catch (error) {
    console.error("Error fetching faculty projects:", error)
    return NextResponse.json(
      { error: "Failed to fetch faculty projects" },
      { status: 500 }
    )
  }
}

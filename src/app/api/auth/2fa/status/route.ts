import { NextResponse } from "next/server"
import { db } from "@/drizzle/db"
import { users } from "@/drizzle/schema"
import { eq } from "drizzle-orm"
import { getServerSession } from "next-auth"

import { authOptions } from "@/configs/next-auth"

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    const [user] = await db
      .select({
        totpEnabled: users.totpEnabled,
      })
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1)

    if (!user) {
      return NextResponse.json({ message: "User not found" }, { status: 404 })
    }

    return NextResponse.json({
      enabled: user.totpEnabled,
    })
  } catch (error) {
    console.error("Failed to get 2FA status:", error)
    return NextResponse.json(
      { message: "Failed to get 2FA status" },
      { status: 500 }
    )
  }
}

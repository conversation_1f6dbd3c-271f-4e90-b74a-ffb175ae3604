/**
 * API Route: Disable 2FA for authenticated user
 * Requires current password and 2FA token verification
 */

import { db } from "@/drizzle/db"
import { users } from "@/drizzle/schema"
import bcrypt from "bcryptjs"
import { eq } from "drizzle-orm"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"
import { Result, err, ok } from "@/lib/result"
import {
  decryptBackupCodes,
  decryptTotpSecret,
  verifyTwoFactor,
} from "@/lib/totp-service"

interface DisableTwoFactorRequest {
  readonly password: string
  readonly token: string
}

/**
 * POST /api/auth/2fa/disable
 * Disables 2FA for the authenticated user
 */
export async function POST(request: NextRequest): Promise<Response> {
  // Parse request body
  let body: DisableTwoFactorRequest
  const bodyText = await request.text()

  if (!bodyText) {
    return Response.json({ error: "Request body is required" }, { status: 400 })
  }

  const parseResult = (() => {
    const parsed = JSON.parse(bodyText) as DisableTwoFactorRequest
    if (!parsed.password || typeof parsed.password !== "string") {
      return err("Current password is required")
    }
    if (!parsed.token || typeof parsed.token !== "string") {
      return err("2FA token is required")
    }
    return ok(parsed)
  })()

  if (!parseResult.success) {
    return Response.json({ error: parseResult.error }, { status: 400 })
  }

  body = parseResult.data

  // Get authenticated user session
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    return Response.json({ error: "Authentication required" }, { status: 401 })
  }

  // Get user from database with password
  const userResult = await db.query.users.findFirst({
    where: eq(users.id, session.user.id),
    columns: {
      id: true,
      password: true,
      totpSecret: true,
      totpEnabled: true,
      totpBackupCodes: true,
      totpLastUsed: true,
    },
  })

  if (!userResult) {
    return Response.json({ error: "User not found" }, { status: 404 })
  }

  if (!userResult.totpEnabled) {
    return Response.json(
      { error: "2FA is not enabled for this account" },
      { status: 400 }
    )
  }

  if (!userResult.password) {
    return Response.json(
      { error: "Password verification required" },
      { status: 400 }
    )
  }

  // Verify current password
  const passwordValid = await bcrypt.compare(body.password, userResult.password)
  if (!passwordValid) {
    return Response.json({ error: "Invalid password" }, { status: 400 })
  }

  // Verify 2FA token before disabling
  if (!userResult.totpSecret) {
    return Response.json({ error: "TOTP secret not found" }, { status: 500 })
  }

  const decryptedSecretResult = decryptTotpSecret(userResult.totpSecret)
  if (!decryptedSecretResult.success) {
    return Response.json(
      { error: `Failed to decrypt secret: ${decryptedSecretResult.error}` },
      { status: 500 }
    )
  }

  // Get backup codes
  let backupCodes: readonly string[] = []
  if (
    userResult.totpBackupCodes &&
    Array.isArray(userResult.totpBackupCodes) &&
    userResult.totpBackupCodes.length > 0
  ) {
    const decryptedCodesResult = decryptBackupCodes(
      userResult.totpBackupCodes[0] as string
    )
    if (decryptedCodesResult.success) {
      backupCodes = decryptedCodesResult.data
    }
  }

  // Verify the 2FA token
  const lastUsedTimestamp = userResult.totpLastUsed?.getTime()
  const verificationResult = verifyTwoFactor(
    decryptedSecretResult.data,
    body.token,
    backupCodes,
    lastUsedTimestamp
  )

  if (!verificationResult.success) {
    return Response.json(
      { error: `2FA verification failed: ${verificationResult.error}` },
      { status: 400 }
    )
  }

  if (!verificationResult.data.isValid) {
    return Response.json({ error: "Invalid 2FA token" }, { status: 400 })
  }

  // Disable 2FA by clearing all 2FA-related fields
  const updateResult = await db
    .update(users)
    .set({
      totpEnabled: false,
      totpSecret: null,
      totpBackupCodes: null,
      totpLastUsed: null,
      updatedAt: new Date(),
    })
    .where(eq(users.id, session.user.id))
    .returning({ id: users.id })
    .then(() => ok(true))
    .catch((err) => err(`Database update failed: ${err.message}`))

  if (!updateResult.success) {
    return Response.json({ error: updateResult.error }, { status: 500 })
  }

  return Response.json(
    {
      message: "2FA has been successfully disabled for your account",
      enabled: false,
    },
    { status: 200 }
  )
}

import { NextResponse } from "next/server"
import { db } from "@/drizzle/db"
import { deliverables, projectStudents, projects } from "@/drizzle/schema"
import { and, count, eq } from "drizzle-orm"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"

/**
 * Get statistics for the current faculty member
 */
export async function GET(_request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id || session.user.role !== "faculty") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get total projects count
    const totalProjectsResult = await db
      .select({ count: count() })
      .from(projects)
      .where(eq(projects.facultyId, session.user.id))

    const totalProjects = totalProjectsResult[0]?.count || 0

    // Get active projects count
    const activeProjectsResult = await db
      .select({ count: count() })
      .from(projects)
      .where(
        and(
          eq(projects.facultyId, session.user.id),
          eq(projects.status, "active")
        )
      )

    const activeProjects = activeProjectsResult[0]?.count || 0

    // Get unique students count
    const studentsResult = await db
      .selectDistinct({ studentId: projectStudents.studentId })
      .from(projectStudents)
      .innerJoin(projects, eq(projectStudents.projectId, projects.id))
      .where(eq(projects.facultyId, session.user.id))

    const assignedStudents = studentsResult.length

    // Get deliverables stats
    const deliverableStats = await db
      .select({
        total: count(),
        graded: count(deliverables.grade),
      })
      .from(deliverables)
      .innerJoin(projects, eq(deliverables.projectId, projects.id))
      .where(eq(projects.facultyId, session.user.id))

    const totalDeliverables = deliverableStats[0]?.total || 0
    const gradedDeliverables = deliverableStats[0]?.graded || 0

    const stats = {
      totalProjects,
      activeProjects,
      assignedStudents,
      totalDeliverables,
      gradedDeliverables,
      pendingReviews: totalDeliverables - gradedDeliverables,
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error("Faculty stats fetch error:", error)
    return NextResponse.json(
      { error: "Failed to fetch statistics" },
      { status: 500 }
    )
  }
}

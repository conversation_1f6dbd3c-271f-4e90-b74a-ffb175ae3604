export type SettingsType = {
  theme: string
  mode: "light" | "dark" | "system"
  radius: number
  layout: "vertical" | "horizontal"
  locale: string
}

export type ModeType = "light" | "dark" | "system"

export type LocaleType = "en" | "ar"

export type FormatStyleType =
  | "decimal"
  | "currency"
  | "percent"
  | "unit"
  | "duration"
  | "regular"

export type FileType = {
  id: string
  filename: string
  name?: string // Added for backwards compatibility with chat components
  type?: string // Added for chat components that use type instead of mimetype
  url: string
  size: number
  mimetype: string
  uploadedBy?: string
  createdAt: Date | string
}

export type UserType = {
  id: string
  name: string
  email: string
  avatar?: string
  status?: string
  role?: string
  // Extended properties for full user profiles
  firstName?: string
  lastName?: string
  password?: string
  username?: string
  background?: string
  phoneNumber?: string
  state?: string
  country?: string
  address?: string
  zipCode?: string
  language?: string
  timeZone?: string
  currency?: string
  organization?: string
  organizationName?: string
  organizationTitle?: string
  twoFactorAuth?: boolean
  loginAlerts?: boolean
  accountReoveryOption?: string
  connections?: number
  followers?: number
}

export type DirectionType = "ltr" | "rtl"

export type IconType = React.ComponentType<{ className?: string }>

export type IconProps = {
  className?: string
  size?: string | number
  color?: string
  style?: React.CSSProperties
}

export type DynamicIconNameType = string

export type SignInFormType = {
  email: string
  password: string
}

export type RegisterFormType = {
  firstName: string
  lastName: string
  email: string
  password: string
  confirmPassword: string
  role: string
  organizationName?: string
  titleInOrganization?: string
}

export type ForgotPasswordFormType = {
  email: string
}

export type NavigationRootItem = {
  title: string
  icon?: string
  url?: string
  items?: NavigationNestedItem[]
}

export type NavigationNestedItem = {
  title: string
  url: string
  icon?: string
}

export type ThemeType = {
  name: string
  cssVars: {
    light: Record<string, string>
    dark: Record<string, string>
  }
}

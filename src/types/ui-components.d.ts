// Type declarations for UI components to fix TS71007 errors
// This explicitly declares that onOpenChange and similar props are not Server Actions

import type { ReactNode } from "react"

// Dialog component props
export interface DialogProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  children?: ReactNode
}

// Sheet component props
export interface SheetProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  children?: ReactNode
}

// Popover component props
export interface PopoverProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  children?: ReactNode
}

// Dropdown menu component props
export interface DropdownMenuProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  children?: ReactNode
}

// Command component props
export interface CommandProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  children?: ReactNode
}

// Drawer component props
export interface DrawerProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  children?: ReactNode
}

// Alert dialog component props
export interface AlertDialogProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  children?: ReactNode
}

// Context menu component props
export interface ContextMenuProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  children?: ReactNode
}

// Hover card component props
export interface HoverCardProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  children?: ReactNode
}

// Navigation menu component props
export interface NavigationMenuProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  children?: ReactNode
}

// Collapsible component props
export interface CollapsibleProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  children?: ReactNode
}

// Accordion component props
export interface AccordionProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  children?: ReactNode
}

// Tabs component props
export interface TabsProps {
  value?: string
  onValueChange?: (value: string) => void
  children?: ReactNode
}

// Select component props
export interface SelectProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  value?: string
  onValueChange?: (value: string) => void
  children?: ReactNode
}

// Combobox component props
export interface ComboboxProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  value?: string
  onValueChange?: (value: string) => void
  children?: ReactNode
}

// Calendar component props
export interface CalendarProps {
  selected?: Date
  onSelect?: (date: Date | undefined) => void
  children?: ReactNode
}

// Date picker component props
export interface DatePickerProps {
  date?: Date
  onDateChange?: (date: Date | undefined) => void
  children?: ReactNode
}

// Switch component props
export interface SwitchProps {
  checked?: boolean
  onCheckedChange?: (checked: boolean) => void
  children?: ReactNode
}

// Checkbox component props
export interface CheckboxProps {
  checked?: boolean
  onCheckedChange?: (checked: boolean) => void
  children?: ReactNode
}

// Radio group component props
export interface RadioGroupProps {
  value?: string
  onValueChange?: (value: string) => void
  children?: ReactNode
}

// Slider component props
export interface SliderProps {
  value?: number[]
  onValueChange?: (value: number[]) => void
  children?: ReactNode
}

// Toggle component props
export interface ToggleProps {
  pressed?: boolean
  onPressedChange?: (pressed: boolean) => void
  children?: ReactNode
}

// Toast component props
export interface ToastProps {
  open?: boolean
  onOpenChange?: (open: boolean) => void
  children?: ReactNode
}

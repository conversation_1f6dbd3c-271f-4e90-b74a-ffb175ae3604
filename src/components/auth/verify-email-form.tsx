"use client"

import * as React from "react"
import Link from "next/link"
import { useParams } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"

import type { VerifyEmailFormType } from "@/schemas/verify-email-schema"
import type { LocaleType } from "@/types/i18n"

import { VerifyEmailSchema } from "@/schemas/verify-email-schema"

import { ensureLocalizedPathname } from "@/lib/i18n"
import { cn } from "@/lib/utils"

import { toast } from "@/hooks/use-toast"
import { ButtonLoading, buttonVariants } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"

export function VerifyEmailForm() {
  const params = useParams()
  const form = useForm<VerifyEmailFormType>({
    resolver: zodR<PERSON>olver(VerifyEmailSchema),
    defaultValues: {
      email: "",
    },
  })

  const locale = params.lang as LocaleType
  const [isResending, setIsResending] = React.useState(false)

  async function onSubmit(data: VerifyEmailFormType) {
    setIsResending(true)
    try {
      const response = await fetch("/api/auth/verify-email", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      const responseData = await response.json()

      if (!response.ok) {
        throw new Error(
          responseData.error || "Failed to send verification email"
        )
      }

      toast({
        title: "Check your email",
        description:
          responseData.message ||
          "We've sent you an email with instructions to verify your email address.",
      })

      form.reset()
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Something went wrong",
        description:
          error instanceof Error
            ? error.message
            : "Failed to send verification email",
      })
    } finally {
      setIsResending(false)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="grid gap-4">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input type="email" placeholder="Enter your email" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <ButtonLoading type="submit" isLoading={isResending} className="w-full">
          {isResending ? "Sending..." : "Resend Verification Email"}
        </ButtonLoading>

        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">Or</span>
          </div>
        </div>

        <Link
          href={ensureLocalizedPathname(
            process.env.NEXT_PUBLIC_HOME_PATHNAME || "/",
            locale
          )}
          className={cn(buttonVariants({ variant: "outline" }), "w-full")}
        >
          Skip for now
        </Link>
      </form>
    </Form>
  )
}

import React from "react"
import { <PERSON><PERSON>own, ArrowDownUp, <PERSON><PERSON><PERSON>, <PERSON>Off } from "lucide-react"

import type { Column } from "@tanstack/react-table"
import type { ComponentProps } from "react"

import { cn } from "@/lib/utils"

import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface DataTableColumnHeaderProps<TData, TValue>
  extends ComponentProps<"div"> {
  column: Column<TData, TValue>
  title: string
  className?: string
}

export function DataTableColumnHeader<TData, TValue>({
  column,
  title,
  className,
}: DataTableColumnHeaderProps<TData, TValue>) {
  if (!column.getCanSort()) {
    return <div className={cn(className)}>{title}</div>
  }

  return (
    <div className={cn("flex items-center gap-x-2", className)}>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="-ms-3 h-8 data-[state=open]:bg-accent"
          >
            <span>{title}</span>
            {column.getIsSorted() === "desc" ? (
              <ArrowDown className="ms-2 size-3" />
            ) : column.getIsSorted() === "asc" ? (
              <ArrowUp className="ms-2 size-3" />
            ) : (
              <ArrowDownUp className="ms-2 size-3" />
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start">
          {React.createElement(DropdownMenuItem as React.ComponentType<any>, {
            onClick: () => column.toggleSorting(false)
          },
            React.createElement(ArrowUp, { className: "me-2 h-4 w-4 text-muted-foreground/70" }),
            "Asc"
          )}
          {React.createElement(DropdownMenuItem as React.ComponentType<any>, {
            onClick: () => column.toggleSorting(true)
          },
            React.createElement(ArrowDown, { className: "me-2 h-4 w-4 text-muted-foreground/70" }),
            "Desc"
          )}
          <DropdownMenuSeparator />
          {React.createElement(DropdownMenuItem as React.ComponentType<any>, {
            onClick: () => column.toggleVisibility(false)
          },
            React.createElement(EyeOff, { className: "me-2 h-4 w-4 text-muted-foreground/70" }),
            "Hide"
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

import * as React from "react"
import { Slot } from "@radix-ui/react-slot"

import type { ComponentProps } from "react"

import { cn } from "@/lib/utils"

export function BentoGrid({ className, ...props }: ComponentProps<"div">) {
  return (
    <div
      data-slot="bento-grid"
      className={cn("w-full grid gap-4 mx-auto md:grid-cols-3", className)}
      {...props}
    />
  )
}

export function BentoItem({ className, ...props }: ComponentProps<"div">) {
  return (
    <div
      data-slot="bento-item"
      className={cn(
        "group/bento flex flex-col justify-between gap-6 p-6 bg-card text-card-foreground rounded-lg border overflow-hidden",
        className
      )}
      {...props}
    />
  )
}

export function BentoHeader({ className, ...props }: ComponentProps<"div">) {
  return (
    <div
      data-slot="bento-header"
      className={cn("flex-1 w-full h-full select-none", className)}
      {...props}
    />
  )
}

export function BentoContent({ className, ...props }: ComponentProps<"div">) {
  return (
    <div
      data-slot="bento-content"
      className={cn("space-y-1.5", className)}
      {...props}
    />
  )
}

const BentoTitle = React.forwardRef<
  HTMLHeadingElement,
  ComponentProps<"h2"> & {
    asChild?: boolean
    children?: React.ReactNode
    className?: string
  }
>(({ className, asChild, children, ...props }, ref) => {
  if (asChild) {
    const SlotComponent = Slot as React.ComponentType<
      ComponentProps<"h2"> & {
        children?: React.ReactNode
        className?: string
        ref?: React.Ref<HTMLElement>
      }
    >

    return (
      <SlotComponent
        data-slot="bento-title"
        ref={ref}
        className={cn("font-semibold leading-none tracking-tight", className)}
        {...props}
      >
        {children}
      </SlotComponent>
    )
  }

  return (
    <h2
      data-slot="bento-title"
      ref={ref}
      className={cn("font-semibold leading-none tracking-tight", className)}
      {...props}
    >
      {children}
    </h2>
  )
})
BentoTitle.displayName = "BentoTitle"

export function BentoDescription({
  className,
  ...props
}: ComponentProps<"div">) {
  return (
    <p
      data-slot="bento-description"
      className={cn("text-sm text-muted-foreground", className)}
      {...props}
    />
  )
}

"use client"

import * as React from "react"
import * as AccordionPrimitive from "@radix-ui/react-accordion"
import { ChevronDownIcon } from "lucide-react"

import { cn } from "@/lib/utils"

const Accordion = React.forwardRef<
  React.ComponentRef<typeof AccordionPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Root> & {
    children?: React.ReactNode
    className?: string
  }
>(({ children, className, ...props }, ref) => {
  const RootComponent = AccordionPrimitive.Root as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Root> & {
      children?: React.ReactNode
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <RootComponent
      data-slot="accordion"
      ref={ref}
      className={className}
      {...props}
    >
      {children}
    </RootComponent>
  )
})
Accordion.displayName = "Accordion"

const AccordionItem = React.forwardRef<
  React.ComponentRef<typeof AccordionPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item> & {
    className?: string
    children?: React.ReactNode
  }
>(({ className, children, ...props }, ref) => {
  const ItemComponent = AccordionPrimitive.Item as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item> & {
      children?: React.ReactNode
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <ItemComponent
      ref={ref}
      data-slot="accordion-item"
      className={cn("border-b last:border-b-0", className)}
      {...props}
    >
      {children}
    </ItemComponent>
  )
})
AccordionItem.displayName = "AccordionItem"

const AccordionTrigger = React.forwardRef<
  React.ComponentRef<typeof AccordionPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger> & {
    className?: string
    children?: React.ReactNode
  }
>(({ className, children, ...props }, ref) => {
  const HeaderComponent = AccordionPrimitive.Header as React.ComponentType<{
    className?: string
    children?: React.ReactNode
  }>

  return (
    <HeaderComponent className="flex">
      <AccordionPrimitive.Trigger
        ref={ref}
        data-slot="accordion-trigger"
        className={cn(
          "focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180",
          className
        )}
        {...props}
      >
        {children}
        <ChevronDownIcon className="text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200" />
      </AccordionPrimitive.Trigger>
    </HeaderComponent>
  )
})
AccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName

const AccordionContent = React.forwardRef<
  React.ComponentRef<typeof AccordionPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content> & {
    className?: string
    children?: React.ReactNode
  }
>(({ className, children, ...props }, ref) => {
  const ContentComponent = AccordionPrimitive.Content as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content> & {
      children?: React.ReactNode
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <ContentComponent
      ref={ref}
      data-slot="accordion-content"
      className="data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm"
      {...props}
    >
      <div className={cn("pt-0 pb-4", className)}>{children}</div>
    </ContentComponent>
  )
})
AccordionContent.displayName = AccordionPrimitive.Content.displayName

export { Accordion, AccordionItem, AccordionTrigger, AccordionContent }

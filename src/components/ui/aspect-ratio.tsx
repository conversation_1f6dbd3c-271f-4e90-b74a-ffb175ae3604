"use client"

import * as React from "react"
import * as AspectRatioPrimitive from "@radix-ui/react-aspect-ratio"

const AspectRatio = React.forwardRef<
  React.ComponentRef<typeof AspectRatioPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof AspectRatioPrimitive.Root> & {
    children?: React.ReactNode
    className?: string
  }
>(({ children, className, ...props }, ref) => {
  const RootComponent = AspectRatioPrimitive.Root as React.ComponentType<any>

  return (
    <RootComponent
      data-slot="aspect-ratio"
      ref={ref}
      className={className}
      {...props}
    >
      {children}
    </RootComponent>
  )
})
AspectRatio.displayName = AspectRatioPrimitive.Root.displayName

export { AspectRatio }

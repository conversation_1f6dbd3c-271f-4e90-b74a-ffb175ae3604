import * as React from "react"
import { Slot } from "@radix-ui/react-slot"

import type { ComponentProps } from "react"

import { cn } from "@/lib/utils"

const StickyLayout = React.forwardRef<
  HTMLDivElement,
  ComponentProps<"div"> & {
    asChild?: boolean
    children?: React.ReactNode
    className?: string
  }
>(({ className, asChild, children, ...props }, ref) => {
  if (asChild) {
    const SlotComponent = Slot as React.ComponentType<
      ComponentProps<"div"> & {
        children?: React.ReactNode
        className?: string
        ref?: React.Ref<HTMLElement>
      }
    >

    return (
      <SlotComponent
        {...({ "data-slot": "sticky-layout" } as any)}
        ref={ref}
        className={cn("grid items-start gap-4 lg:grid-cols-2", className)}
        {...props}
      >
        {children}
      </SlotComponent>
    )
  }

  return (
    <div
      {...({ "data-slot": "sticky-layout" } as any)}
      ref={ref}
      className={cn("grid items-start gap-4 lg:grid-cols-2", className)}
      {...props}
    >
      {children}
    </div>
  )
})
StickyLayout.displayName = "StickyLayout"

export function StickyLayoutPane({
  className,
  ...props
}: ComponentProps<"div">) {
  return (
    <div
      data-slot="sticky-layout-pane"
      className={cn(
        "top-20 flex flex-col items-center text-center space-y-1.5 lg:sticky lg:block lg:text-start",
        className
      )}
      {...props}
    />
  )
}

export function StickyLayoutContent({
  className,
  ...props
}: ComponentProps<"div">) {
  return (
    <div
      data-slot="sticky-layout-content"
      className={cn("space-y-4", className)}
      {...props}
    />
  )
}

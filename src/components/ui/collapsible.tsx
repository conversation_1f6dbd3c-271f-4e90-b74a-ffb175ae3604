"use client"

import * as React from "react"
import * as CollapsiblePrimitive from "@radix-ui/react-collapsible"

const Collapsible = React.forwardRef<
  React.ComponentRef<typeof CollapsiblePrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.Root> & {
    children?: React.ReactNode
    className?: string
  }
>(({ children, className, ...props }, ref) => {
  const RootComponent = CollapsiblePrimitive.Root as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.Root> & {
      children?: React.ReactNode
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <RootComponent
      data-slot="collapsible"
      ref={ref}
      className={className}
      {...props}
    >
      {children}
    </RootComponent>
  )
})
Collapsible.displayName = CollapsiblePrimitive.Root.displayName

const CollapsibleTrigger = React.forwardRef<
  React.ComponentRef<typeof CollapsiblePrimitive.CollapsibleTrigger>,
  React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.CollapsibleTrigger> & {
    children?: React.ReactNode
    className?: string
  }
>(({ children, className, ...props }, ref) => {
  const TriggerComponent = CollapsiblePrimitive.CollapsibleTrigger as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.CollapsibleTrigger> & {
      children?: React.ReactNode
      className?: string
      ref?: React.Ref<HTMLButtonElement>
    }
  >

  return (
    <TriggerComponent
      data-slot="collapsible-trigger"
      ref={ref}
      className={className}
      {...props}
    >
      {children}
    </TriggerComponent>
  )
})
CollapsibleTrigger.displayName = CollapsiblePrimitive.CollapsibleTrigger.displayName

const CollapsibleContent = React.forwardRef<
  React.ComponentRef<typeof CollapsiblePrimitive.CollapsibleContent>,
  React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.CollapsibleContent> & {
    children?: React.ReactNode
    className?: string
  }
>(({ children, className, ...props }, ref) => {
  const ContentComponent = CollapsiblePrimitive.CollapsibleContent as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.CollapsibleContent> & {
      children?: React.ReactNode
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <ContentComponent
      data-slot="collapsible-content"
      ref={ref}
      className={className}
      {...props}
    >
      {children}
    </ContentComponent>
  )
})
CollapsibleContent.displayName = CollapsiblePrimitive.CollapsibleContent.displayName

export { Collapsible, CollapsibleTrigger, CollapsibleContent }

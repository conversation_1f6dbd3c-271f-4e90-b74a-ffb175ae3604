"use client"

import * as React from "react"
import * as ProgressPrimitive from "@radix-ui/react-progress"

import { cn } from "@/lib/utils"

const Progress = React.forwardRef<
  React.ComponentRef<typeof ProgressPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root> & {
    className?: string
  }
>(({ className, value, ...props }, ref) => {
  const RootComponent = ProgressPrimitive.Root as React.ComponentType<any>
  const IndicatorComponent = ProgressPrimitive.Indicator as React.ComponentType<any>

  return (
    <RootComponent
      data-slot="progress"
      ref={ref}
      className={cn(
        "bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",
        className
      )}
      {...props}
    >
      <IndicatorComponent
        data-slot="progress-indicator"
        className="bg-primary h-full w-full flex-1 transition-all"
        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}
      />
    </RootComponent>
  )
})
Progress.displayName = ProgressPrimitive.Root.displayName

export { Progress }

"use client"

import * as React from "react"
import * as CheckboxPrimitive from "@radix-ui/react-checkbox"
import { CheckIcon } from "lucide-react"

import { cn } from "@/lib/utils"

// Module augmentation to extend CheckboxIndicatorProps
declare module "@radix-ui/react-checkbox" {
  interface CheckboxIndicatorProps {
    children?: React.ReactNode
  }
}

const Checkbox = React.forwardRef<
  React.ComponentRef<typeof CheckboxPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root> & {
    className?: string
    id?: string
  }
>(({ className, ...props }, ref) => {
  const RootComponent = CheckboxPrimitive.Root as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root> & {
      className?: string
      ref?: React.Ref<HTMLButtonElement>
    }
  >

  const IndicatorComponent = CheckboxPrimitive.Indicator as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Indicator> & {
      children?: React.ReactNode
      className?: string
    }
  >

  return (
    <RootComponent
      data-slot="checkbox"
      ref={ref}
      className={cn(
        "peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",
        className
      )}
      {...props}
    >
      <IndicatorComponent
        data-slot="checkbox-indicator"
        className="flex items-center justify-center text-current transition-none"
      >
        <CheckIcon className="size-3.5" />
      </IndicatorComponent>
    </RootComponent>
  )
})
Checkbox.displayName = CheckboxPrimitive.Root.displayName

export { Checkbox }

"use client"

import * as React from "react"
import * as TooltipPrimitive from "@radix-ui/react-tooltip"

import { cn } from "@/lib/utils"

// Module augmentation for Tooltip components
declare module "@radix-ui/react-tooltip" {
  interface TooltipContentProps {
    className?: string
    children?: React.ReactNode
  }
}

const TooltipProvider = React.forwardRef<
  React.ComponentRef<typeof TooltipPrimitive.Provider>,
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Provider> & {
    delayDuration?: number
  }
>(({ delayDuration = 0, ...props }, ref) => {
  const ProviderComponent = TooltipPrimitive.Provider as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Provider> & {
      ref?: React.Ref<HTMLDivElement>
    }
  >
  return (
    <ProviderComponent
      data-slot="tooltip-provider"
      ref={ref}
      delayDuration={delayDuration}
      {...props}
    />
  )
})
TooltipProvider.displayName = "TooltipProvider"

const Tooltip = React.forwardRef<
  React.ComponentRef<typeof TooltipPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Root>
>(({ ...props }, ref) => {
  const RootComponent = TooltipPrimitive.Root as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Root> & {
      ref?: React.Ref<HTMLDivElement>
    }
  >
  return (
    <TooltipProvider>
      <RootComponent data-slot="tooltip" ref={ref} {...props} />
    </TooltipProvider>
  )
})
Tooltip.displayName = "Tooltip"

const TooltipTrigger = React.forwardRef<
  React.ComponentRef<typeof TooltipPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Trigger>
>(({ ...props }, ref) => {
  const TriggerComponent = TooltipPrimitive.Trigger as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Trigger> & {
      ref?: React.Ref<HTMLButtonElement>
    }
  >
  return <TriggerComponent data-slot="tooltip-trigger" ref={ref} {...props} />
})
TooltipTrigger.displayName = "TooltipTrigger"

const TooltipContent = React.forwardRef<
  React.ComponentRef<typeof TooltipPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content> & {
    className?: string
    sideOffset?: number
    children?: React.ReactNode
  }
>(({ className, sideOffset = 0, children, ...props }, ref) => {
  const ContentComponent = TooltipPrimitive.Content as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content> & {
      className?: string
      children?: React.ReactNode
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <TooltipPrimitive.Portal>
      <ContentComponent
        data-slot="tooltip-content"
        ref={ref}
        sideOffset={sideOffset}
        className={cn(
          "bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",
          className
        )}
        {...props}
      >
        {children}
        <TooltipPrimitive.Arrow className="bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]" />
      </ContentComponent>
    </TooltipPrimitive.Portal>
  )
})
TooltipContent.displayName = "TooltipContent"

export { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }

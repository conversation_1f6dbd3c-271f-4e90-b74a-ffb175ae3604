{"permissions": {"allow": ["mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(pnpm add:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(pnpm db:generate:*)", "Bash(pnpm db:migrate:*)", "Bash(pnpm db:push:*)", "Bash(node:*)", "Bash(rm:*)", "Bash(pnpm remove:*)", "WebFetch(domain:next-auth.js.org)", "Bash(git init:*)", "mcp__github__create_repository", "Bash(gh auth:*)", "Bash(git config:*)", "Bash(git remote add:*)", "Bash(git push:*)", "Bash(git remote remove:*)", "Bash(pnpm dev:*)", "Bash(pnpm i:*)", "Bash(grep:*)", "Bash(git checkout:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(npm run typecheck:*)", "Bash(npx tsc:*)", "Bash(npx eslint:*)", "Bash(pnpm db:seed:*)", "Bash(pnpm tsx:*)", "mcp__puppeteer__puppeteer_navigate", "Bash(lsof:*)", "mcp__puppeteer__puppeteer_screenshot", "mcp__puppeteer__puppeteer_fill", "mcp__puppeteer__puppeteer_click", "mcp__puppeteer__puppeteer_evaluate", "Bash(kill:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "mcp__playwright__playwright_navigate", "mcp__playwright__playwright_fill", "mcp__playwright__playwright_click", "mcp__playwright__playwright_screenshot", "mcp__playwright__playwright_get_visible_text", "mcp__playwright__playwright_evaluate", "mcp__playwright__playwright_console_logs", "mcp__playwright__playwright_close", "mcp__playwright__start_codegen_session", "<PERSON><PERSON>(touch:*)", "Bash(pnpm run:*)", "Bash(find:*)", "Bash(npx payload generate:types:*)", "Bash(pnpm list:*)", "Bash(PAYLOAD_CONFIG_PATH=./payload.config.ts pnpm exec payload generate:types)", "Bash(git reset:*)", "<PERSON><PERSON>(mv:*)", "Bash(pnpm list:*)", "Bash(pnpm payload:generate:types:*)", "Bash(find:*)", "mcp__desktop-commander__read_file", "mcp__desktop-commander__search_files", "mcp__desktop-commander__list_directory", "Bash(npm run build:*)", "Bash(ls:*)", "Bash(pnpm payload generate:types:*)", "Bash(npm run payload:*)", "Bash(npm run lint:*)", "Bash(npm run format:*)", "mcp__ide__getDiagnostics", "Bash(npm run type-check:*)", "<PERSON><PERSON>(npx prettier:*)", "Bash(pnpm payload migrate:*)", "Bash(pnpm tsc:*)", "Bash(pnpm lint:*)", "Bash(pnpm exec eslint:*)", "<PERSON><PERSON>(sed:*)", "Bash(awk:*)", "mcp__jetbrains__get_project_problems", "mcp__jetbrains__get_current_file_errors", "mcp__jetbrains__get_all_open_file_paths", "Bash(NODE_OPTIONS='--import tsx' node test-payload.ts)", "<PERSON><PERSON>(openssl rand:*)", "Bash(NODE_OPTIONS='--import tsx' node test-db.ts)", "Bash(NODE_OPTIONS='--import tsx' node test-parse-uri.ts)", "Bash(NODE_OPTIONS='--import tsx' pnpm payload migrate:create initial)", "Bash(NODE_OPTIONS='--import tsx' pnpm payload migrate)", "Bash(NODE_OPTIONS='--import tsx' pnpm payload migrate:status)", "Bash(NODE_OPTIONS='--import tsx' pnpm run create-admin)", "Bash(NODE_OPTIONS='--import tsx' node test-payload-connection.ts)", "mcp__thinking-server__think", "Bash(pnpm drizzle-kit generate:*)", "mcp__jetbrains__open_file_in_editor", "Bash(psql:*)", "Bash(npm run:*)", "Bash(pnpm prettier:*)", "Bash(pdftotext:*)", "Bash(npx tsx:*)", "<PERSON><PERSON>(head:*)", "Bash(pnpm typecheck:*)", "Bash(pnpm type-check:*)", "Bash(pnpm build:*)", "Bash(pnpm drizzle-kit push:*)", "Bash(pnpm db:studio:*)", "mcp__jetbrains__get_file_text_by_path", "mcp__jetbrains__replace_specific_text", "WebFetch(domain:docs.bullmq.io)", "Bash(pnpm test:*)", "<PERSON><PERSON>(curl:*)", "mcp__jetbrains__list_available_actions", "mcp__jetbrains__search_in_files_content", "mcp__jetbrains__replace_file_text_by_path", "mcp__jetbrains__execute_terminal_command", "mcp__jetbrains__get_terminal_text", "mcp__jetbrains__wait", "Bash(rg:*)", "WebFetch(domain:github.com)", "mcp__github__get_file_contents", "WebFetch(domain:raw.githubusercontent.com)"], "deny": []}, "enableAllProjectMcpServers": false}
import "dotenv/config"
import bcrypt from "bcryptjs"
import { db } from "../src/drizzle/db"
import { users } from "../src/drizzle/schema"
import { eq } from "drizzle-orm"

async function createFirstAdmin() {
  try {
    // Check if any admin exists
    const existingAdmins = await db
      .select()
      .from(users)
      .where(eq(users.role, "admin"))
      .limit(1)

    if (existingAdmins.length > 0) {
      console.log("Admin user already exists")
      return
    }

    // Hash password
    const hashedPassword = await bcrypt.hash("Admin123!", 10)

    // Create first admin
    const [admin] = await db
      .insert(users)
      .values({
        email: "<EMAIL>",
        password: hashedPassword,
        name: "Admin User",
        username: "admin",
        role: "admin",
        emailVerified: new Date(),
      })
      .returning()

    console.log("Admin user created successfully!")
    console.log("Email: <EMAIL>")
    console.log("Password: Admin123!")
    console.log("Please change this password after first login")
  } catch (error) {
    console.error("Error creating admin:", error)
  } finally {
    process.exit(0)
  }
}

createFirstAdmin()
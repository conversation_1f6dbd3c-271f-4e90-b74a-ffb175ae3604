# Questions and Clarifications Needed

## Critical Architectural Questions

### 1. Authentication & Security
- **2FA Implementation**: Is two-factor authentication required for all users or just admins? `All users.`
- **Session Management**: What should the session timeout be? Different for different roles? `1 hour.`
- **Password Policy**: Minimum requirements? Force periodic changes? `Minimum 8 characters, at least one letter, at least one number, at least one special character. No easy passwords. No periodic changes.`
- **OAuth Providers**: Which providers should we support beyond current implementation? `You figure it out.`
- **API Security**: Should we implement API keys for potential third-party integrations? `Yes.`

### 2. Data Retention & Compliance
- **GDPR Compliance**: What's the data retention policy for:
  - User accounts after deletion? `1 year.`
  - Application data after rejection? `1 year.`
  - Interview recordings/notes? `1 year.`
  - Chat messages? `1 year.`
- **Audit Log Retention**: How long should we keep audit logs? `1 year.`
- **Right to be Forgotten**: How do we handle deletion requests while maintaining system integrity? `Purge all data.`
- **Data Breach Response**: What's the process for reporting and responding to breaches? `Report to me immediately.`
- **Compliance Audits**: How often should we undergo compliance audits? `Annually.`
- **Consent Management**: How do we handle consent for data sharing and processing? `Ask for consent.`
- **Data Residency**: Any requirements for data to stay in Canada? `No.`

### 3. Performance & Scalability
- **Concurrent Users**: What's the expected peak load? `Around 300 concurrent users`
  - During application deadlines? `300.`
  - During matching runs? `300.`
  - During result announcements? `300`
- **File Size Limits**: Maximum sizes for:
  - PDF transcripts? `5MB.`
  - Profile photos? `2MB.`
  - Supporting documents? `5MB.`
- **Matching Algorithm Scale**: How many students/organizations do we expect? `300 students, 100 orgs`
  - Current O(n²) complexity could be problematic at scale `What do you mean here?`

## Feature-Specific Questions

### 4. Student Application Process
- **Draft Retention**: How long should we keep draft applications after submission? `1 year.`
- **Application Amendments**: Can students modify applications after submission but before deadline? `Yes.`
- **Multiple Applications**: Can a student apply to multiple streams in one cycle? `Yes.`
- **Withdrawal Process**: How should students withdraw applications? `Withdraw button with confirmation.`
- **Re-application**: Can rejected students reapply in future cycles? `Create toggle for this in admin dashboard.`

### 5. Grade Processing
- **Grade Scale Variations**: How do we handle:
  - Pass/Fail courses? `0`
  - Incomplete grades? `0`
  - Courses in progress? `0`
  - Transfer credits? `0`
  - International transcripts? `0`
- **Grade Verification**: Should admins manually verify all grade imports?  `No, unless there is an anomaly like 5 As or an error.`
- **Grade Updates**: Can students update grades after initial submission? `No. Contact admin.`

### 6. Matching Algorithm Details
- **Tie Breaking Rules**: When match scores are identical, what's the precedence?
  - Application timestamp? `Yes`
  - Random selection? `No`
  - Additional criteria? `What else would you recommend?`
- **Partial Matches**: Should we support:
  - Part-time placements? `No.`
  - Job-sharing between students? 'No.'
  - Split placements (multiple organizations)? `No.`
- **Match Rejection**: What happens when:
  - A student rejects all matches? `Student is removed from matching pool.`
  - An organization rejects all matches? `Re-run matches for organization.`
  - A match falls through after acceptance? `Automatically match next student`
- **Waitlist Management**: How does the waitlist work? `Students who apply late, who reject all matches, or submit incomplete applications are waitlisted`
  - Automatic promotion? `No.`
  - Re-ranking after rejections? `Yes.`
  - Notification timing? `As soon as they are matched.`

### 7. Faculty Supervision
- **Co-supervision**: Should multiple faculty supervise one student? `No.`
- **Supervision Transfer**: What happens when faculty:
  - Go on sabbatical? `Not an issue.`
  - Leave the institution? `Not an issue.`
  - Become unavailable mid-term? `Not an issue.`
- **Supervision Limits**: Is there a maximum number of students per faculty? `Yes, 5`
- **Cross-institutional**: Can faculty from other schools participate? `No.`

### 8. Organization Integration
- **Panel Interviews**: How do we handle multiple interviewers? `Not an issue.`
  - Scheduling coordination? `Org handles scheduling for multiple interviewers. `
  - Consolidated feedback? `360 feedback mechanism.`
  - Decision weighting? `Explain what you mean.`
- **Organizational Hierarchies**: Do we need:
  - Department-level access? `No.`
  - Approval workflows? `Yes, for all deliverables.`
  - Delegated authorities? `Yes, for mentors`
- **External Systems**: Should we integrate with:
  - Organizational HR systems? `No.`
  - Calendar systems (Exchange/Google)? `Yes, optional integration if user wants it, but ICS is default`
  - Video conferencing (Zoom/Teams)? `Offer integration with Zoom, Google Meet, Teams, etc. Allow sign-in via OAuth.`

### 9. Communication & Notifications
- **Message Retention**: How long do we keep:
  - Direct messages? `1 year.`
  - System notifications? `3 months`
  - Email archives? `1 year.`
- **Communication Boundaries**: Can:
  - Students message each other? `No.`
  - Faculty message students before matching? `No.`
  - Organizations contact students directly? `Yes.`
- **Notification Preferences**: Should users control:
  - Frequency (immediate/digest)? `Yes.`
  - Channel (email/SMS/in-app)? `Yes.`
  - Type (all/critical only)? `Yes.`

## Technical Implementation Questions

### 10. File Management
- **S3 Bucket Structure**: How should we organize:
  - User uploads by year/cycle? `Organize by year.`
  - Separate buckets for different file types? `Is this necessary?`
  - Archive strategy for old files? `Archive adfter 6 months.`
- **File Access Control**: 
  - Temporary signed URLs? `No.`
  - Permanent URLs with access control? `Yes.`
  - Download tracking? `Yes`
- **File Processing**: Should we:
  - Auto-compress large PDFs? `Yes,`
  - Extract text for searching? `Yes.`
  - Generate thumbnails? `No.`

### 11. Email System
- **Template Management**: Who can edit email templates? `Admins Only`
  - Through UI or code only? `Through UI`
  - Version control for templates? `Yes.`
  - A/B testing capability? `Yes.`
- **Email Tracking**: Should we track:
  - Open rates? `Yes.`
  - Click rates? `Yes.`
  - Bounce handling? `Yes.`
- **Bulk Email**: How do we handle:
  - Rate limiting? `You decide.`
  - Unsubscribe management? `Implement unsubscribe link.`
  - Spam prevention? `Implement anti-spam filters.`

### 12. Reporting & Analytics
- **Standard Reports**: Which reports are essential vs nice-to-have? `Granular control.`
- **Custom Reports**: Should admins build custom reports? `Yes.`
- **Data Export**: What formats needed:
  - Excel with formatting? `Yes.`
  - CSV for data analysis? `Yes.`
  - PDF for board presentations? `Yes.`
- **Real-time Dashboards**: Which metrics need real-time updates? `All.`

## Business Logic Clarifications

### 13. Application Cycles
- **Cycle Management**: How do we handle:
  - Multiple concurrent cycles? `What do you mean?`
  - Rolling admissions? `What do you mean?`
  - Off-cycle applications? `What do you mean?`
- **Cycle Transitions**: What happens to in-progress data when new cycle starts? `What do you mean?`

### 14. Deliverable Management
- **Revision Requests**: Is there a limit on revision rounds? `No.`
- **Late Submissions**: How much flexibility on deadlines? `None.`
- **Grading Disputes**: Is there an appeal process? `No.`
- **Deliverable Types**: Are the 3 fixed deliverables enough or should it be configurable? `Configurable. 3 default.`

### 15. Support System
- **Ticket Routing**: How are tickets assigned to support staff? `All tickets assigned to admin.`
- **Escalation Path**: What triggers escalation? `Manual escalation by user in chat.`
- **Response SLAs**: Expected response times? `1 business day.`
- **Ticket Categories**: Should we have predefined categories? `Yes. Decide which.`

## User Experience Questions

### 16. Accessibility
- **Screen Reader Support**: Any specific screen readers to test with? `You decide.`
- **Keyboard Navigation**: Custom shortcuts needed? `No.`
- **Language Support**: Future multilingual requirements? `Sure. Chinese, French, Spanish`
- **Cognitive Accessibility**: Simplified interface option? `Yes.`

### 17. Mobile Experience
- **Mobile Features**: Should all features work on mobile or subset? `All`
- **Offline Capability**: Any offline requirements? `No.`
- **Native App**: Future native app plans that affect architecture? `No.`

### 18. Training & Onboarding
- **Mandatory Training**: Should we track training completion? `Yes.`
- **Role-specific Tutorials**: Interactive or just videos? `None.`
- **Sandbox Environment**: Should users practice in a test environment? `No.`

## Edge Cases & Error Handling

### 19. System Failures
- **Matching Algorithm Failure**: Manual fallback process? `Print failure message to admin`
- **Payment Processing**: How to handle payment failures (if applicable)? `No payments.`
- **Document Processing**: Fallback for OCR failures beyond tickets? `Yes. Secondary OCR.`
- **Integration Failures**: Graceful degradation when external services fail?  `Yes.`

### 20. Data Conflicts
- **Duplicate Applications**: How to handle accidental duplicates? `Prompt to delete or merge`
- **Concurrent Edits**: Last-write-wins or conflict resolution? `Conflict resolution.`
- **Data Migration**: How to handle schema changes with existing data? `You decide...try non-destructive.`

## Future Considerations

### 21. ML Enhancement
- **Training Data**: How do we collect training data ethically? `You decide.`
- **Bias Prevention**: How do we ensure algorithm fairness? `Algorithm is based on mutual interest, there is no inherent bias.`
- **Transparency**: How much of ML logic should be explainable? `All of it.`

### 22. Integration Roadmap
- **LMS Integration**: Future plans for Canvas/Blackboard? `Maybe, yes.`
- **Identity Providers**: SAML/LDAP for enterprise SSO? `Not now.`
- **Payment Systems**: If adding paid programs? `Not now.`

## Immediate Clarifications Needed

### High Priority (Blocking Development)
1. **S3 Configuration**: Bucket names, regions, IAM policies? `You decide.`
2. **Email Provider**: Resend API key and sender domains? `In .env`
3. **Production Database**: Connection details and backup strategy? `Railway`
4. **Domain Names**: Production URLs for CORS configuration? `Not yet.`
5. **SSL Certificates**: Who manages these? `What do you mean?`

### Medium Priority (Affects Architecture)
1. **Background Job System**: Use Next.js API routes or separate service? `You decide. What is most efficient and performant?`
2. **Caching Strategy**: Redis cluster or simple in-memory? `What is most efficient and performant?`
3. **Search Implementation**: Elasticsearch or database full-text? `What is most efficient and performant?`
4. **File Virus Scanning**: Required for uploads? `Yes. We have VirusTotal in .env!!!!`
5. **Rate Limiting**: Limits per endpoint per role? `What should we do here?`

### Low Priority (Can Decide Later)
1. **Analytics Platform**: Google Analytics, Plausible, or custom? `Plausible`
2. **Error Tracking**: Sentry, LogRocket, or custom? `Sentry`
3. **Feature Flags**: System for gradual rollouts? `Sure.`
4. **A/B Testing**: Framework for experiments? `Yes.`
5. **Documentation Platform**: Where to host user docs? `You decide.`

## Ambiguities in Current Requirements

### 23. Role Definitions
- Are there sub-roles within each main role? `Only for orgs, designated mentor`
- Can users have multiple roles? `No. But admins should be able to impersonate any user`
- How do we handle role transitions? `No role transitions except assigned by admin.`

### 24. Workflow States
- Are the current states (draft, submitted, in-review, etc.) sufficient? `Yes.`
- Can workflows move backwards (e.g., approved back to in-review)? `No.`
- Who can trigger state transitions? `Automatic, but admins can override. Does this make sense?`

### 25. Business Rules
- Minimum/maximum students per organization? `Min 1, Max 6`
- Minimum/maximum projects per faculty? `Min 1, max 4`
- Geographic restrictions for matches? `Canada/US/UK only`
- Conflict of interest rules? `Cannot be assigned to family or friends`

## Questions from Implementation Experience

### 26. Why Decisions
- Why Gale-Shapley over other matching algorithms? `What others exist?`
- Why 40-point grade scale specifically? `Because of sliding grade scale. What would you have done?`
- Why 7/35/70 day deliverable schedule? `Because learning plan must be completed in first week. Midpoint is obviously in fifth week...and final reflection in 10th week.`
- Why these specific weight percentages for matching? `Because that is the way I conceived of it. What would you have done?`

### 27. Missing Features
- No mention of video interviews - intentional? `Is it possible to implement this? How?`
- No group projects mentioned - future feature? `Possibly.`
- No peer evaluations - considered? `Eventually.`
- No alumni involvement - planned? `Eventually.`

### 28. Technical Debt
- Should we refactor the placeholder implementations now or post-MVP? `Refactor now`
- Is the current database schema normalized enough? `What do you think?`
- Should we add database indexes before production? `What do you think?`

## Process Questions

### 29. Development Workflow
- Code review process? 
- Deployment approval process?
- Hotfix procedures?
- Rollback strategies?

### 30. Testing Requirements
- Unit test coverage target?
- Integration test requirements?
- User acceptance testing process?
- Performance benchmarks?

---

*Note: These questions arose from reviewing the implementation against the requirements documents. Many represent gaps between what was built and what's needed for production.*